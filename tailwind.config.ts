import type { Config } from "tailwindcss";
import colors from "./themePalette";
export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./constants.ts"
  ],
  theme: {
    extend: {
      colors: {
        ...colors
      },
      screens: {
        'sm': { 'min': '728px' },   // For medium screens (max-width: 767px)
        'md': { 'min': '810px' },   // For medium screens (max-width: 767px)
        '-sm': { 'max': '728px' },   // For small screens (max-width: 639px)
        '-md': { 'max': '810px' },   // For medium screens (max-width: 767px)
        '-mmd': { 'max': '809px' },   // For medium screens (max-width: 767px)
        '-lg': { 'max': '1023px' },  // For large screens (max-width: 1023px)
        '-xl': { 'max': '1279px' },  // For extra large screens (max-width: 1279px)
        '-2xl': { 'max': '1535px' }, // For 2x extra large screens (max-width: 1535px)
      },
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
      animation: {
        shimmer: 'shimmer 1.5s infinite',
      },
    },
  },
  plugins: [],
} satisfies Config;
