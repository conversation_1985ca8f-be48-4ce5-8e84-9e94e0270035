export const EMAIL_VISIBILITY = {
  full: "FULL",
  partial: "PARTIAL",
  none: "NONE",
} as const;

export type Tag = {
  text: string;
  value: string;
  wrapperClass?: string;
  borderClass?: string;
  bgClass?: string;
  textClass?: string;
};

export type TagValue = "lead" | "client" | "partner" | "friend" | "family" | "colleague";

export const TAGVALUE_OPTIONS: TagValue[] = ["lead", "client", "partner", "friend", "family", "colleague"]

export const TAGS: Tag[] = [
  {
    text: "Lead",
    value: "lead",
    wrapperClass: "bg-yellow-50 rounded-xl",
    textClass: "text-yellow-800 font-semibold",
    borderClass: "border-yellow-800",
    bgClass: "bg-yellow-800",
  },
  {
    text: "Client",
    value: "client",
    wrapperClass: "bg-red-50 rounded-xl",
    textClass: "text-red-600 font-semibold",
    borderClass: "border-red-600",
    bgClass: "bg-red-600",
  },
  {
    text: "Partner",
    value: "partner",
    wrapperClass: "bg-teal-50 rounded-xl",
    textClass: "text-teal-800 font-semibold",
    borderClass: "border-teal-800",
    bgClass: "bg-teal-800",
  },
  {
    text: "Friend",
    value: "friend",
    wrapperClass: "bg-gray-100 rounded-xl",
    textClass: "text-gray-900 font-semibold",
    borderClass: "border-gray-900",
    bgClass: "bg-gray-900",
  },
  {
    text: "Family",
    value: "family",
    wrapperClass: "bg-blue-50 rounded-xl",
    textClass: "text-blue-600 font-semibold",
    borderClass: "border-blue-600",
    bgClass: "bg-blue-600",
  },
  {
    text: "Colleague",
    value: "colleague",
    wrapperClass: "bg-gray-100 rounded-xl",
    textClass: "text-gray-900 font-semibold",
    borderClass: "border-gray-900",
    bgClass: "bg-gray-900",
  },
];

export const TAG_DICT: Record<string, Tag> = {
  Lead: {
    text: "Lead",
    value: "lead",
    wrapperClass: "bg-yellow-50 rounded-xl",
    textClass: "text-yellow-800 font-semibold",
    borderClass: "border-yellow-800",
    bgClass: "bg-yellow-800",
  },
  Client: {
    text: "Client",
    value: "client",
    wrapperClass: "bg-red-50 rounded-xl",
    textClass: "text-red-600 font-semibold",
    borderClass: "border-red-600",
    bgClass: "bg-red-600",
  },
  Partner: {
    text: "Partner",
    value: "partner",
    wrapperClass: "bg-teal-50 rounded-xl",
    textClass: "text-teal-800 font-semibold",
    borderClass: "border-teal-800",
    bgClass: "bg-teal-800",
  },
  Friend: {
    text: "Friend",
    value: "friend",
    wrapperClass: "bg-gray-100 rounded-xl",
    textClass: "text-gray-900 font-semibold",
    borderClass: "border-gray-900",
    bgClass: "bg-gray-900",
  },
  Family: {
    text: "Family",
    value: "family",
    wrapperClass: "bg-blue-50 rounded-xl",
    textClass: "text-blue-600 font-semibold",
    borderClass: "border-blue-600",
    bgClass: "bg-blue-600",
  },
  Colleague: {
    text: "Colleague",
    value: "colleague",
    wrapperClass: "bg-gray-100 rounded-xl",
    textClass: "text-gray-900 font-semibold",
    borderClass: "border-gray-900",
    bgClass: "bg-gray-900",
  },
};

export const CARD_THEMES = [
  { id: 0, color: "bg-black", hex: "#000000", textColorHex: "#ffffff", blackCheck: false },
  { id: 1, color: "bg-[#FDFDFD]", hex: "#FDFDFD", textColorHex: "#000000", blackCheck: true },
];



export type EmailVisibility = typeof EMAIL_VISIBILITY[keyof typeof EMAIL_VISIBILITY];
