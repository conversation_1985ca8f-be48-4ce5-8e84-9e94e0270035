// useAuthStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  user: any;              // or more specific type if you have one
  setUser: (user: any) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user: any) => set({ user }),
      logout: () => set({ user: null }),
    }),
    {
      name: "auth-storage", // unique name for localStorage
      partialize: (state) => ({ user: state.user }), // only persist the user property
    }
  )
);
