import { create } from "zustand";
import { persist } from "zustand/middleware";
import { Testimonial } from "../../interfaces";

// Define the types with more flexibility
export interface CardDetails {
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  colour?: number | null;
  logoImageUrl?: string;
  cardType?: string;
  logoWidth?: number;
  logoHeight?: number;
  quantity?: number;
  price?: number;
  id?: number;
  cardId?: number;
  invertLogoColors?: boolean;
  imageFileName?: string | null;
}

export interface CartItem {
  cardTemplateId?: number;
  id: number;
  quantity: number;
  price: number;
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  colour?: number | null;
  logoImageUrl?: string;
  cardType?: string;
  logoWidth?: number;
  logoHeight?: number;
  cardId?: number;
}

export interface NavigationData {
  cardTemplate?: Record<string, any>;
  testimonial?: Testimonial;
  cardDetails?: CardDetails;
  cart?: CartItem[];
  emailAddress?: string;
  contactData?: Record<string, any>;
  [key: string]: any;
}

export interface NavigationState {
  data: NavigationData;
  setData: (data: Partial<NavigationData>) => void;
  updateCardDetails: (cardDetails: Partial<CardDetails>) => void;
  updateCart: (cart: CartItem[]) => void;
  clearData: () => void;
}

// Create the store
export const useNavigationStore = create<NavigationState>()(
  persist(
    (set) => ({
      data: {},
      
      // Set data method
      setData: (newData: Partial<NavigationData>) => 
        set((state) => ({
          data: { ...state.data, ...newData }
        })),
      
      // Update card details method
      updateCardDetails: (cardDetails: Partial<CardDetails>) => 
        set((state) => {
          // Create a new object to avoid mutation
          const newData = { ...state.data };
          
          // Update cardDetails, ensuring it exists
          newData.cardDetails = {
            ...(newData.cardDetails || {}),
            ...cardDetails
          };
          
          // Return the new state
          return { data: newData };
        }),
      
      // Update cart method
      updateCart: (cart: CartItem[]) => 
        set((state) => {
          // Create a new object to avoid mutation
          const newData = { ...state.data };
          
          // Update cart
          newData.cart = cart;
          
          // Return the new state
          return { data: newData };
        }),
      
      // Clear data method
      clearData: () => set({ data: {} }),
    }),
    {
      name: "navigation-storage", // unique name for localStorage
      partialize: (state) => ({ data: state.data }), // only persist the data property
    }
  )
);
