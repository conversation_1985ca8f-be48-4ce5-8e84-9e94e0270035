import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CartItem {
    id: number | string;
    name: string;
    price: number;
    quantity: number;
}

interface CartState {
    items: CartItem[];
    totalQuantity: number;
    totalPrice: number;
}

const initialState: CartState = {
    items: [],
    totalQuantity: 0,
    totalPrice: 0,
};

const cartSlice = createSlice({
    name: 'cart',
    initialState,
    reducers: {
        addItem: (state, action: PayloadAction<{ id: number | string; name: string; price: number; quantity?: number }>) => {
            const { id, name, price, quantity = 1 } = action.payload;
            const existingItem = state.items.find(item => item.id === id);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                state.items.push({ id, name, price, quantity });
            }
            state.totalQuantity += quantity;
            state.totalPrice += price * quantity;
        },
        decrementItem: (state, action: PayloadAction<number | string>) => {
            const id = action.payload;
            const existingItem = state.items.find(item => item.id === id);
            if (existingItem) {
                if (existingItem.quantity > 1) {
                    existingItem.quantity -= 1;
                    state.totalQuantity -= 1;
                    state.totalPrice -= existingItem.price;
                } else {
                    // Remove completely if quantity reaches 0
                    state.items = state.items.filter(item => item.id !== id);
                    state.totalQuantity -= 1;
                    state.totalPrice -= existingItem.price;
                }
            }
        },
        setItemQuantity: (state, action: PayloadAction<{ id: number | string; quantity: number }>) => {
            const { id, quantity } = action.payload;
            if (quantity <= 0) return; // Do nothing if quantity is invalid
            const existingItem = state.items.find(item => item.id === id);
            if (existingItem) {
                const quantityDifference = quantity - existingItem.quantity;
                existingItem.quantity = quantity;
                state.totalQuantity += quantityDifference;
                state.totalPrice += existingItem.price * quantityDifference;
            } else {
                state.items.push({ id, name: '', price: 0, quantity });
                state.totalQuantity += quantity;
            }
        },
        removeItem: (state, action: PayloadAction<number | string>) => {
            const id = action.payload;
            const existingItem = state.items.find(item => item.id === id);
            if (existingItem) {
                state.totalQuantity -= existingItem.quantity;
                state.totalPrice -= existingItem.price * existingItem.quantity;
                state.items = state.items.filter(item => item.id !== id);
            }
        },
        clearCart: (state) => {
            state.items = [];
            state.totalQuantity = 0;
            state.totalPrice = 0;
        },
    },
});

export const { addItem, decrementItem, setItemQuantity, removeItem, clearCart } = cartSlice.actions;
export default cartSlice.reducer;
