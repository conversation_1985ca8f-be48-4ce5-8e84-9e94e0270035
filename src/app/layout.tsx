import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Red_Hat_Display } from "next/font/google";
import "./globals.css";
import TanstackQueryClientProvider from "@/contexts/TanstackQueryClientProvider";
import { NotificationProvider } from "@/contexts/NotificationProvider";

const redHatDisplay = Red_Hat_Display({
  subsets: ['latin']
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`flex flex-col ${redHatDisplay.className} antialiased bg-bg min-h-screen overflow-y-auto`}
      >
        <TanstackQueryClientProvider>
          <NotificationProvider>
            <div className="" id="notification-root"></div>
            <div className="fixed z-[10]" id="modal-root"></div>
            {children}
          </NotificationProvider>
        </TanstackQueryClientProvider>
      </body>
    </html>
  );
}
