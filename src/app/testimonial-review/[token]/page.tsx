"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Formik, Form, useFormikContext, Field } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppTextArea from "@/components/formik/FormikAppTextArea";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import client from "@/api/client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getBearerToken, getUserId } from "@/app/tokenStorage";
import { useNotification } from "@/contexts/NotificationProvider";
import { BiPen } from "react-icons/bi";
import AppButton from "@/components/AppButton";
import { jwtDecode } from "jwt-decode";
import { Testimonial } from "../../../../interfaces";
import { EMAIL_VISIBILITY } from "../../../../constants";
import { obscureEmail } from "@/utils/helpers";
import { format } from "date-fns";

const validationSchema = Yup.object({
    writerName: Yup.string().required("Writer name is required"),
    writerRole: Yup.string().required("Role is required"),
    writerCompany: Yup.string().required("Company name is required"),
    testimonial: Yup.string().required("Testimonial is required"),
    emailVisibility: Yup.string().required("Email visibility option is required"),
});

function LiveGreeting() {
    const { values } = useFormikContext<any>();
    const trimmedName = values.writerName?.trim() || "there";

    return (
        <div className="text-[1.5rem] font-black">Hi {trimmedName}, 👋</div>
    );
}

function LivePreview({ emailAddress }: { emailAddress: string }) {
    const { values } = useFormikContext<any>();

    const trimmed = (v?: string) => v?.trim() || "";
    const visibility = values.emailVisibility || EMAIL_VISIBILITY.full;

    return (
        <div className="p-[1rem] bg-zinc-100 rounded-lg">
            <div className="text-[0.94rem] font-medium text-subtext">
                {trimmed(values.testimonial)}
            </div>
            <div className="text-[0.92rem] font-black text-headertext mt-[0.5rem]">
                {trimmed(values.writerName)}
            </div>
            <div className="text-[0.9rem] font-medium text-subtext">
                {trimmed(values.writerRole)}, {trimmed(values.writerCompany)}
            </div>
            <div className="text-[0.75rem] font-medium text-subtext">
                Approved by {trimmed(values.writerName).split(" ")[0]}{" "}
                {obscureEmail(emailAddress, visibility) ? `(${obscureEmail(emailAddress, visibility)})` : ""} on {format(new Date(), "MMM d, yyyy")}
            </div>
        </div>
    );
}

export default function TestimonialReview() {
    const router = useRouter();
    const [errorMessage, setErrorMessage] = useState("");
    const [editMode, setEditMode] = useState(false);
    const { showNotification } = useNotification();
    const [requesterName, setRequesterName] = useState("");
    const { token } = useParams();

    const { data: testimonial, isLoading } = useQuery({
        queryKey: ["testimonial-by-id"],
        queryFn: async () => {
            const payload = jwtDecode(token as string) as {
                exp: number;
                testimonialId: number;
                userId: number;
                requesterName: string;
            };

            setRequesterName(payload.requesterName)
            // console.log(payload)

            const response = await client.get(`/testimonials/${payload.testimonialId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            return response.data.data as Testimonial;
        },
    });

    const mutation = useMutation({
        mutationFn: async (values: any) => {
            setErrorMessage("");
            const payload = jwtDecode(token as string) as {
                exp: number;
                testimonialId: number;
                userId: number;
                requesterName: string;
            };
            return client.put(
                `/testimonials/${payload.testimonialId}`,
                { ...values, userId: payload.userId, status: true },
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
        },
        onSuccess: (response) => {
            showNotification({
                renderObj: { midSection: "Testimonial approved" },
                type: "success",
            });
            router.push("/login");
        },
        onError: (error: any) => {
            setErrorMessage(
                error?.response?.data?.message || error?.message || "Testimonial submission failed. Please try again."
            );
        },
    });

    const handleEditToggle = (resetFormTouched: () => void) => {
        setEditMode((prev) => !prev);
        resetFormTouched();
    };

    if (isLoading) {
        return (
            <div className="w-screen h-screen px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[10rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        );
    }

    return (
        <div className="flex-1 flex relative flex-col pt-[1.19rem] px-[1rem] md:px-[4.38rem]">
            <div className="flex-1 pt-[0.5rem] flex flex-col items-center">
                <div className="w-full sm:w-[37rem] pb-[5rem]">
                    <Formik
                        initialValues={{
                            writerName: testimonial!.writerName || " ",
                            writerRole: testimonial!.writerRole || " ",
                            writerCompany: testimonial!.writerCompany || " ",
                            testimonial: testimonial!.testimonial || " ",
                            emailVisibility: testimonial!.emailVisibility || EMAIL_VISIBILITY.full,
                            optionalNote: testimonial!.optionalNote,
                        }}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            mutation.mutate(values, {
                                onSettled: () => setSubmitting(false),
                            });
                        }}
                    >
                        {({ isSubmitting, setFieldTouched }) => {
                            const resetFormTouched = () => {
                                setFieldTouched("writerName", false);
                                setFieldTouched("writerRole", false);
                                setFieldTouched("writerCompany", false);
                                setFieldTouched("testimonial", false);
                                setFieldTouched("emailVisibility", false);
                            };

                            return (
                                <Form className="flex flex-col mt-[1.81rem] gap-[0.75rem]">
                                    <LiveGreeting />

                                    <div className="mt-[1rem] text-headertext text-[0.95rem] font-medium">
                                        Here's the draft testimonial submitted by {requesterName} for your review. You can confirm it as-is or make edits before submitting.
                                    </div>

                                    {errorMessage && (
                                        <div className="border border-red-500 bg-red-100 text-red-500 text-sm text-center px-[1rem] py-[0.5rem] rounded-lg font-semibold my-4">
                                            {errorMessage}
                                        </div>
                                    )}

                                    <div className="p-[1rem] border border-outline rounded-lg flex flex-col gap-[0.75rem]">
                                        <FormikAppTextArea name="testimonial" label="Testimonial" placeholder="Write the testimonial" rows={5} />

                                        <div className="flex text-[0.9rem] font-semibold mt-[2rem] justify-between items-center">
                                            <div>Preview - How your testimonial will appear:</div>
                                            <div onClick={() => handleEditToggle(resetFormTouched)} className="flex gap-[0.5rem] rounded hover:bg-zinc-100 items-center px-2 py-2 cursor-pointer">
                                                <BiPen className="text-[1rem]" />
                                                <div>Edit Info</div>
                                            </div>
                                        </div>

                                        <LivePreview emailAddress={testimonial!.emailAddress as string} />

                                        {editMode && (
                                            <div className="p-[1rem] bg-zinc-100 rounded-lg flex flex-col">
                                                <FormikAppTextInput name="writerName" placeholder="Enter writer's name" />
                                                <FormikAppTextInput name="writerRole" placeholder="Enter role" />
                                                <FormikAppTextInput name="writerCompany" placeholder="Enter company name" />

                                                <div className="text-[0.9rem] font-semibold text-headertext mb-[0.4rem]">Email visibility in testimonial:</div>
                                                <div className="flex flex-col gap-[0.5rem]">
                                                    <label className="flex items-center gap-[0.5rem]">
                                                        <Field type="radio" name="emailVisibility" value={EMAIL_VISIBILITY.full} className="accent-headertext" />
                                                        <div className="text-[0.9rem] font-semibold text-headertext">Show full email</div>
                                                    </label>
                                                    <label className="flex items-center gap-[0.5rem]">
                                                        <Field type="radio" name="emailVisibility" value={EMAIL_VISIBILITY.partial} className="accent-headertext" />
                                                        <div className="text-[0.9rem] font-semibold text-headertext">Show obfuscated email</div>
                                                    </label>
                                                    <label className="flex items-center gap-[0.5rem]">
                                                        <Field type="radio" name="emailVisibility" value={EMAIL_VISIBILITY.none} className="accent-headertext" />
                                                        <div className="text-[0.9rem] font-semibold text-headertext">Don't show email</div>
                                                    </label>
                                                </div>
                                            </div>
                                        )}

                                        {/* <FormikAppTextArea name="optionalNote" label={`Optional message or note (only seen by ${requesterName}):`} placeholder="Eg. Sunita, what do you think of this BeyondCard product, will you recommend it" rows={5} /> */}

                                        <div className="flex gap-[1rem] mt-[1.5rem]">
                                            <FormikAppSubmit text="Confirm & Submit" widthClass="px-[1rem] text-[0.9rem] font-medium" heightClass="py-[0.75rem]" />
                                            {/* <AppButton text="Decline & Report" alt widthClass="px-[1rem] text-[0.9rem] font-medium" heightClass="py-[0.75rem]" /> */}
                                        </div>
                                    </div>
                                </Form>
                            );
                        }}
                    </Formik>
                </div>
            </div>
        </div>
    );
}
