"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import AppButton from "@/components/AppButton";
import { SocialProofCard } from "@/components/SocialProofCard";
import Modal from "@/components/Modal";
import AppTextInput from "@/components/AppTextInput";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import client from "@/api/client";
import { getBearerToken, getToken, getUserId } from "@/app/tokenStorage";
import { useNavigationStore } from "@/store/navigationStore";
import { Testimonial } from "../../../../interfaces";
import { useNotification } from "@/contexts/NotificationProvider";
import { useMediaQuery } from "usehooks-ts";
import { obscureEmail, trimmed } from "@/utils/helpers";
import { format } from "date-fns";


export default function Testimonials() {
    const router = useRouter();
    const [openDropDownIndex, setOpenDropDownIndex] = useState<number>();
    const [mode, setMode] = useState("edit");
    const [errorMessage, setErrorMessage] = useState("");
    const [selectedTestimonial, setSelectedTestimonial] = useState<Testimonial>();
    const queryClient = useQueryClient();
    const { setData } = useNavigationStore();
    const { showNotification } = useNotification();

    const isMobile = useMediaQuery('(max-width: 728px)');

    const tableHeaders = ["Customer Name", "Role", "Business/Company Name", "Email", "Status", "Message", ""];
    const columnSizeRatios = [1, 1, 1, 1, 1, 0.5, 0.25];

    const { data: testimonials, isLoading, error } = useQuery({
        queryKey: ["testimonials"],
        queryFn: async () => {
            const response = await client.get(`/testimonials/user/${getUserId()}`, {
                headers: {
                    Authorization: getBearerToken()
                }
            });
            return response.data.data;
        },
    });

    const testimonialUpdate = useMutation({
        mutationFn: async (values: any) => {
            await client.put(`/testimonials/${values.id}`,
                {
                    userId: parseInt(getUserId() as string), ...values
                },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            setSelectedTestimonial(undefined);
            setOpenDropDownIndex(undefined);
            showNotification({
                renderObj: { midSection: "Testimonial updated" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["testimonials"] });
        },
        onError: (error: any) => {
            console.error("Testimonial update failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Testimonial update failed. Please try again." },
                type: "failure",
            });
            setErrorMessage(
                error?.response?.data?.message || error?.message || "Testimonial update failed. Please try again."
            );
        },
    });

    const resendTestimonialRequest = useMutation({
        mutationFn: async (id: number) => {
            await client.post(`/testimonials/resend`,
                {
                    userId: parseInt(getUserId() as string), testimonialId: id
                },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            setSelectedTestimonial(undefined);
            setOpenDropDownIndex(undefined)
            showNotification({
                renderObj: { midSection: "Testimonial request sent" },
                type: "success",
            });
            // queryClient.invalidateQueries({ queryKey: ["testimonials"] });
        },
        onError: (error: any) => {
            console.error("Testimonial request resend failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Testimonial request resend failed. Please try again." },
                type: "failure",
            });
            setErrorMessage(
                error?.response?.data?.message || error?.message || "Testimonial request resend failed. Please try again."
            );
        },
    });

    const deleteTestimonial = useMutation({
        mutationFn: async (id: number) => {
            await client.delete(`/testimonials/${id}`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["testimonials"] });
            setSelectedTestimonial(undefined);
            setOpenDropDownIndex(undefined);
            showNotification({
                renderObj: { midSection: "Testimonial deleted successfully" },
                type: "success",
            });
        },
        onError: (error: any) => {
            console.error("Testimonial deletion failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Testimonial deletion failed. Please try again." },
                type: "failure",
            });
            setErrorMessage(
                error?.response?.data?.message || "Testimonial deletion failed. Please try again."
            );
        },
    });


    if (isLoading) {
        return (
            <div className="flex-1 px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[8.5rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        );
    }

    console.log(selectedTestimonial)

    return (
        <>
            <Modal isOpen={selectedTestimonial != undefined}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="flex items-end gap-[1rem] text-[0.875rem] text-headertext font-medium">
                                {mode == "edit" ? "Testimonial message" : "Delete testimonial"}
                                {isMobile && <span
                                    className={`px-2 py-1 rounded-lg text-xs font-semibold ${selectedTestimonial?.status
                                        ? "bg-teal-100 text-teal-600"
                                        : "bg-amber-100 text-amber-600"
                                        }`}
                                >
                                    {selectedTestimonial?.status ? "Approved" : "Pending"}
                                </span>}
                            </div>
                            <img
                                onClick={() => setSelectedTestimonial(undefined)}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.19rem]">
                            <div className="text-[0.875rem] text-placeholder">
                                {mode == "edit" ? selectedTestimonial?.testimonial : (
                                    <div className="text-subtext">
                                        Are you sure you want to delete testimonial from <span className="text-headertext font-medium">{selectedTestimonial?.writerName}</span>? This action cannot be reversed
                                    </div>
                                )}
                            </div>

                            {mode == "edit" && (
                                <div className="mt-[0.5rem]">
                                    <div className="text-[0.9375rem] font-bold text-headertext">{selectedTestimonial?.writerName}</div>
                                    <div className="flex items-center gap-[0.25rem]">
                                        <div className="text-[0.875rem] text-placeholder">{selectedTestimonial?.writerRole},</div>
                                        <div className="text-[0.875rem] text-placeholder">{selectedTestimonial?.writerCompany}</div>
                                    </div>
                                    {
                                        selectedTestimonial?.status &&
                                        <div className="text-[0.75rem] font-medium text-subtext">
                                            Approved by {trimmed(selectedTestimonial?.writerName).split(" ")[0]}{" "}
                                            {obscureEmail(selectedTestimonial?.emailAddress, selectedTestimonial?.emailVisibility)
                                                ? `(${obscureEmail(
                                                    selectedTestimonial?.emailAddress,
                                                    selectedTestimonial?.emailVisibility
                                                )})`
                                                : ""}{" "}
                                            {selectedTestimonial?.approvalTime
                                                ? `on ${format(new Date(selectedTestimonial.approvalTime), "MMM d, yyyy")}`
                                                : ""}
                                        </div>
                                    }
                                </div>
                            )}

                            <div className="flex gap-[0.75rem]">
                                {mode == "delete" && (
                                    <div className="flex w-full mt-[1.14rem]">
                                        <div
                                            onClick={() => setSelectedTestimonial(undefined)}
                                            className="flex flex-1 cursor-pointer font-medium text-black border border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]"
                                        >
                                            Cancel
                                        </div>
                                    </div>
                                )}
                                <div className="flex w-full gap-[0.65rem] mt-[1.14rem]">
                                    {mode == "edit" && (
                                        <div
                                            onClick={() => setMode("delete")}
                                            className="sm:hidden flex flex-1 cursor-pointer font-medium bg-white text-black border border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]"
                                        >
                                            Remove
                                        </div>
                                    )}
                                    {mode !== "edit" ? <div
                                        onClick={() => {
                                            if (!deleteTestimonial.isPending) {
                                                deleteTestimonial.mutate(selectedTestimonial?.id as number);
                                            }
                                        }}
                                        className="flex flex-1 cursor-pointer font-medium bg-black text-white border border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]"
                                    >
                                        {deleteTestimonial.isPending ? "Deleting..." : mode === "edit" ? "Request edit" : "Delete"}
                                    </div> :
                                        !isMobile ?
                                            <AppButton onClick={() => { setSelectedTestimonial(undefined) }} widthClass="flex-1" text="Done" />
                                            :
                                            !selectedTestimonial?.status &&
                                            <AppButton onClick={() => { resendTestimonialRequest.mutate(selectedTestimonial?.id as number) }} widthClass="flex-1" text="Resend" />
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            {testimonials && testimonials.length > 0 ? (
                <div onClick={() => setOpenDropDownIndex(undefined)} className="flex-1 flex relative flex-col pt-[1.88rem] px-[1rem] lg:px-[3.81rem]">
                    <div className="flex items-center justify-between">
                        <div className="text-[1.125rem] font-semibold text-headertext">Testimonials</div>
                        <AppButton
                            onClick={() => router.push("/testimonials/add")}
                            widthClass="w-[11.81rem] -md:hidden mt-[1.88rem] ml-auto"
                            text="Add testimonial"
                        />
                        <img onClick={() => router.push("/testimonials/add")} className="md:hidden w-[1.5rem] h-[1.5rem]" src="/plus-small.svg" />
                    </div>

                    {/* Mobile view */}
                    <div className="flex flex-1 mt-[1.31rem] sm:hidden">
                        <div className="flex flex-1 flex-col gap-[1.13rem]">
                            {testimonials.map((row: Testimonial, rowIndex: number) => (
                                <div onClick={() => { setSelectedTestimonial(row); setMode("edit") }} key={rowIndex} className="flex flex-col px-[0.7rem] border border-outline rounded-[0.625rem] py-[0.64rem]">
                                    <div className="text-[0.875rem] font-medium text-headertext">{row.writerName}</div>
                                    <div className="flex gap-1 text-[0.9365rem] text-subtext">
                                        <div>{row.writerRole},</div>
                                        <div>{row.writerCompany}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Desktop view */}
                    <div className="flex flex-1 mt-[1.31rem] -sm:hidden">
                        <div className="flex flex-1 flex-col border-t border-outline">
                            <div className="flex w-full h-[3.63rem] px-[1rem] border-b lg:px-[3rem] items-center text-[0.75rem] text-subtext font-bold">
                                {tableHeaders.map((tableHeader, index) => (
                                    <div className={`${index === 3 ? '-xl:hidden' : ''}`} key={index} style={{ flex: columnSizeRatios[index] }}>
                                        {tableHeader}
                                    </div>
                                ))}
                            </div>

                            {testimonials.map((row: Testimonial, rowIndex: number) => (
                                <div
                                    key={rowIndex}
                                    className="flex w-full h-[3.63rem] px-[1rem] lg:px-[3rem] items-center text-[0.75rem] text-subtext border-b border-outline"
                                >
                                    <div style={{ flex: columnSizeRatios[0] }}>{row.writerName}</div>
                                    <div style={{ flex: columnSizeRatios[1] }}>{row.writerRole}</div>
                                    <div style={{ flex: columnSizeRatios[2] }}>{row.writerCompany}</div>
                                    <div className="-xl:hidden" style={{ flex: columnSizeRatios[3] }}>{row.emailAddress || "—"}</div>
                                    <div className="flex gap-1" style={{ flex: columnSizeRatios[4] }}>
                                        <span
                                            className={`px-2 py-1 rounded-lg text-xs font-semibold ${!row.archived ? (row.status
                                                ? "bg-teal-100 text-teal-600"
                                                : "bg-amber-100 text-amber-600")
                                                : "bg-purple-100 text-purple-600"
                                                }`}
                                        >
                                            {row.archived ? "Archived" : row.status ? "Approved" : "Pending"}
                                        </span>
                                    </div>
                                    <div onClick={() => { setSelectedTestimonial(row); setMode("edit") }} style={{ flex: columnSizeRatios[5] }} className="text-orange cursor-pointer">
                                        View
                                    </div>
                                    <div style={{ flex: columnSizeRatios[6] }} className="flex items-center justify-center w-[2rem] cursor-pointer">
                                        <div
                                            className="relative cursor-pointer"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setOpenDropDownIndex(rowIndex);
                                            }}
                                        >
                                            <img className="w-[0.94rem] h-[0.94rem] mr-[1.19rem]" src="/ellipsis.svg" />
                                            {openDropDownIndex === rowIndex && (
                                                <div className="absolute border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem] bg-white rounded-md z-[2]">
                                                    {!row.status &&
                                                        <div
                                                            onClick={() => { resendTestimonialRequest.mutate(row.id as number) }}
                                                            className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext">
                                                            Resend
                                                        </div>
                                                    }
                                                    {
                                                        row.status &&
                                                        <div
                                                            onClick={() => { testimonialUpdate.mutate({ id: row.id as number, archived: !row.archived }) }}
                                                            className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext">
                                                            {row.archived ? "Unarchive" : "Archive"}
                                                        </div>
                                                    }
                                                    <div
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setMode("delete");
                                                            setSelectedTestimonial(row);
                                                        }}
                                                        className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger"
                                                    >
                                                        Remove testimonial
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            ) : (
                <div className="flex-1 flex relative flex-col items-center justify-center">
                    <img src="/comment.svg" className="w-[2.5rem] h-[2.5rem]" />
                    <div className="mt-[0.75rem] font-medium">You have no testimonials yet</div>
                    <div className="mt-[0.56rem] text-[0.875rem] text-subtext w-[13.5rem] text-center">
                        Add testimonials from your clients/customers
                    </div>
                    <AppButton
                        onClick={() => router.push("/testimonials/add")}
                        widthClass="w-[11.81rem] mt-[1.19rem]"
                        text="Add testimonial"
                    />
                </div>
            )}
        </>
    );
}
