"use client";

import { useRouter } from "next/navigation";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppTextArea from "@/components/formik/FormikAppTextArea";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import client from "@/api/client";
import { useMutation } from "@tanstack/react-query";
import { getBearerToken, getToken, getUserId } from "@/app/tokenStorage";
import { useNotification } from "@/contexts/NotificationProvider";

const validationSchema = Yup.object({
    writerName: Yup.string().required("Testifier name is required"),
    writerRole: Yup.string().required("Role is required"),
    writerCompany: Yup.string().required("Company name is required"),
    testimonial: Yup.string().required("Testimonial is required"),
    emailAddress: Yup.string().email().required("Email Address is required"),
});

export default function AddTestimonial() {
    const router = useRouter();
    const [errorMessage, setErrorMessage] = useState("");
    const { showNotification } = useNotification();

    const mutation = useMutation({
        mutationFn: async (values: any) => {
            setErrorMessage("");
            const userId = getUserId();
            return client.post("/testimonials",
                { ...values, userId: userId ? parseInt(userId) : userId },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                }
            );
        },
        onSuccess: (response) => {
            console.log("Testimonial Added:", response.data);
            showNotification({ renderObj: { midSection: "Testimonial request sent" }, type: "success" })
            router.push("/testimonials");
        },
        onError: (error: any) => {
            console.error("Testimonial addition failed:", error);
            setErrorMessage(
                error?.response?.data?.message || "Testimonial addition failed. Please try again."
            );
        },
    });

    return (
        <div className="flex-1 flex relative flex-col pt-[1.19rem] px-[1rem] md:px-[4.38rem]">
            <GoBackButton />
            <div className="flex-1 pt-[0.69rem] flex flex-col items-center">
                <div className="w-full sm:w-[26.88rem] pb-[5rem]">
                    <div className="text-[1.375rem] font-medium">Add Testimonial</div>
                    <div className="mt-[0.5rem] text-subtext">
                        Submit a testimonial request to a colleague, supervisor, or employer.
                    </div>

                    {errorMessage && (
                        <div className="border border-red-500 bg-red-100 text-red-500 text-sm text-center px-[1rem] py-[0.5rem] rounded-lg font-semibold my-4">
                            {errorMessage}
                        </div>
                    )}

                    <Formik
                        initialValues={{
                            writerName: "",
                            writerRole: "",
                            writerCompany: "",
                            testimonial: "",
                            emailAddress: ""
                        }}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            mutation.mutate(values, {
                                onSettled: () => setSubmitting(false),
                            });
                        }}
                    >
                        {({ isSubmitting }) => (
                            <Form className="flex flex-col mt-[1.81rem] gap-[0.75rem]">
                                <FormikAppTextInput name="writerName" label="Testifier's Name" placeholder="Enter writer's name" />
                                <FormikAppTextInput name="writerRole" label="Testifier's Role" placeholder="Enter role" />
                                <FormikAppTextInput name="writerCompany" label="Testifier's Company" placeholder="Enter company name" />
                                <FormikAppTextInput name="emailAddress" type="email" label="Testifier's Email Address" placeholder="Enter email address" />
                                <FormikAppTextArea name="testimonial" label="Testimonial Draft" placeholder="Write the testimonial" rows={5} />

                                <FormikAppSubmit text="Submit Testimonial" widthClass="w-full mt-[1.88rem]" disabled={isSubmitting} />
                            </Form>
                        )}
                    </Formik>
                </div>
            </div>
        </div>
    );
}
