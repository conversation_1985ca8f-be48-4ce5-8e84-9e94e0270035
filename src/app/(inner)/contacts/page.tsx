"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import AppTextInput from "@/components/AppTextInput";
import AppButton from "@/components/AppButton";
import AppTextArea from "@/components/AppTextArea";
import client from "@/api/client";
import { getBearerToken, getToken, getUserId } from "@/app/tokenStorage";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { BsPerson, BsPersonCircle, BsX } from "react-icons/bs";
import { BiEdit, BiEditAlt, BiFilter, BiPlusCircle, BiSearch, BiSearchAlt } from "react-icons/bi";
import { BsCheck2Square, BsSquare } from "react-icons/bs";
import colors from "../../../../themePalette";
import { HiDownload, HiTrash } from "react-icons/hi";
import { TbTagFilled, TbTrash } from "react-icons/tb";
import { FaEllipsisV, FaTags } from "react-icons/fa";
import AppSelectInput from "@/components/AppSelectInput";
import TagSelectInput from "@/components/TagSelectInput";
import { TAG_DICT, TAGS, TagValue, TAGVALUE_OPTIONS } from "../../../../constants";
import { useMediaQuery } from "usehooks-ts";
import Modal from "@/components/Modal";
import { format } from "date-fns";
import TagComponent from "@/components/TagComponent";
import { useNotification } from "@/contexts/NotificationProvider";
import { capitalizeWords, generateVCFInternal } from "@/utils/helpers";
import { Tooltip } from "react-tooltip";

interface GuestName {
    imageUrl: string;
    name: string;
}

interface RowData {
    firstName: string;
    lastName: string;
    emailAddress: string;
    created: string;
    modified: string;
    phoneNumber: string;
    tag: TagValue[];
    jobTitle?: string;
    company?: string;
    notes?: ContactNote[]
    usersId?: number;
    contactId?: number;
    introductoryMessage?: string;
    guestImage?: string;
    status: "ACCEPTED" | "PENDING";
}

interface RowDataSoft {
    firstName: string;
    lastName: string;
    emailAddress: string;
    created: string;
    modified: string;
    phoneNumber: string;
    tag: TagValue[];
    jobTitle?: string;
    company?: string;
    usersId?: number;
    contactId?: number;
    introductoryMessage?: string;
    guestImage?: string;
    status: "ACCEPTED" | "PENDING";
}

export interface ContactNote {
    noteId?: string;
    note: string;
    contactId?: number;
    created?: string;
    modified?: string;
}



// const rowsJSONArray: RowData[] = [
//     {
//         guest_name: {
//             imageUrl: "/avatar.svg",
//             name: "John Doe",
//         },
//         email: "<EMAIL>",
//         phone: "+*********",
//         job_title: "Software Engineer",
//         company: "Tech Corp",
//     },
//     {
//         guest_name: {
//             imageUrl: "/avatar.svg",
//             name: "Jane Smith",
//         },
//         email: "<EMAIL>",
//         phone: "+*********",
//         job_title: "Product Manager",
//         company: "Innovate Ltd",
//     },
// ];

export default function Contacts() {
    const router = useRouter();
    // const [contacts, setcontacts] = useState<RowData[]>(rowsJSONArray);
    const [selectedContact, setSelectedContact] = useState<RowData>();
    const [searchText, setSearchText] = useState("");
    const [tagSelectOpened, setTagSelectOpened] = useState(false);
    const [mobileTagSelectOpened, setMobileTagSelectOpened] = useState(false);
    const [tagFilterOpened, setTagFilterOpened] = useState(false);
    const [introMessageModalOpened, setIntroMessageModalOpened] = useState(false);
    const [deleteContactModalOpened, setDeleteContactModalOpened] = useState(false);
    const [mobileContactModalOpened, setMobileContactModalOpened] = useState(false);
    const [currentSelectedTags, setCurrentSelectedTags] = useState<TagValue[]>([])
    const [tagFilters, setTagFilters] = useState<TagValue[]>([])
    const [contactOldNotes, setContactOldNotes] = useState([])
    const [newNote, setNewNote] = useState<string>("")
    const [noteEditIndex, setNoteEditIndex] = useState<number | undefined>()
    const [isCreateNoteMode, setIsCreateNoteMode] = useState<boolean>(false)
    const [activeMenuIndex, setActiveMenuIndex] = useState<number | null>(null);
    // Add a new state to track which note is being confirmed for deletion
    const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
    // Bulk selection state
    const [selectedContactIds, setSelectedContactIds] = useState<number[]>([]);
    const [isSelectAllChecked, setIsSelectAllChecked] = useState(false);
    // Tab state
    const [activeTab, setActiveTab] = useState<"PENDING" | "ACCEPTED">("PENDING");

    const { showNotification } = useNotification()
    const queryClient = useQueryClient()

    const isMobile = useMediaQuery('(max-width: 728px)');

    const tableHeaders = ["", "Guest", "Contact Information", "Tag", "Date Received", "Intro & Notes", "Action"]
    const tableKeys = ["select", "guest", "contact-info", "tag", "created", "introductoryMessage", "buttons"]
    const columnSizeRatios = [0.3, 1, 1, 0.5, 0.5, 0.5, 0.5]

    const { data: contacts, isLoading, error } = useQuery({
        queryKey: ["contacts", activeTab],
        queryFn: async () => {
            const userId = getUserId();
            const response = await client.get(`/contacts/download_guest/${userId}?status=ACCEPTED`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`
                },
                params: {
                    status: activeTab
                }
            });
            // Assuming the API returns the templates in response.data.data
            return response.data.data as RowData[]
        },
    });



    const getContactData = async (id: number) => {
        const response = await client.get(`/contacts/contact/${id}`, {
            headers: {
                Authorization: getBearerToken()
            }
        });

        setSelectedContact(response.data.data)
        // Assuming the API returns the templates in response.data.data
        return response.data.data as RowData[]
    };

    const tagUpdate = useMutation({
        mutationFn: async (id: number) => {
            await client.put(`/contacts/guest/${id}`,
                {
                    userId: parseInt(getUserId() as string), tag: currentSelectedTags.map((t) => capitalizeWords(t))
                },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Contact tag(s) updated" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
        },
        onError: (error: any) => {
            console.error("Contact tag update failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Contact tag update failed. Please try again." },
                type: "failure",
            });
        },
        onSettled() {
            setCurrentSelectedTags([])
        },
    });

    const createNote = useMutation({
        mutationFn: async ({ contactId, note }: { contactId: number; note: string }) => {
            return await client.post(`/contacts/note`, { contactId, note }, {
                headers: { Authorization: getBearerToken() },
            });
        },
        onSuccess: (response) => {
            setSelectedContact((prev) => {
                if (!prev) return prev;

                return {
                    ...prev,
                    notes: [...(prev.notes || []), response.data.data],
                };
            });
            setIsCreateNoteMode(false)
        },
        onError: (error: any) => {
            showNotification({
                renderObj: {
                    midSection: error?.response?.data?.message || error?.message || "Contact note creation failed. Please try again.",
                },
                type: "failure",
            });
        },
        onSettled: () => {
            setNewNote("")
        }
    });

    const updateNote = useMutation({
        mutationFn: async ({ contactId, note, noteId }: { contactId: number; note: string, noteId: string }) => {
            await client.put(`/contacts/note/${noteId}`, { contactId, note }, {
                headers: { Authorization: getBearerToken() },
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setNoteEditIndex(undefined)
        },
        onError: (error: any) => {
            showNotification({
                renderObj: {
                    midSection: error?.response?.data?.message || error?.message || "Contact note creation failed. Please try again.",
                },
                type: "failure",
            });
        }
    });


    const contactDelete = useMutation({
        mutationFn: async (id: number) => {
            await client.delete(`/contacts/guest/${id}`,
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Contact deleted" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
        },
        onError: (error: any) => {
            console.error("Contact tag deletion failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Contact tag deletion failed. Please try again." },
                type: "failure",
            });
        },
        onSettled: () => {
            setDeleteContactModalOpened(false);
            setSelectedContact(undefined);
        }
    });


    const noteDelete = useMutation({
        mutationFn: async (id: number) => {
            await client.delete(`/contacts/note/${id}`,
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Note deleted" },
                type: "success",
            });
            getContactData(selectedContact?.contactId as number);
        },
        onError: (error: any) => {
            console.error("Contact note deletion failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Contact note deletion failed. Please try again." },
                type: "failure",
            });
        },
    });

    // Bulk accept contacts mutation
    const bulkAcceptContacts = useMutation({
        mutationFn: async (contactIds: number[]) => {
            await client.put(`/contacts/guest/accept`,
                { contactIds },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: `${selectedContactIds.length} contact(s) accepted successfully` },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setSelectedContactIds([]);
            setIsSelectAllChecked(false);
        },
        onError: (error: any) => {
            console.error("Bulk accept failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Failed to accept contacts. Please try again." },
                type: "failure",
            });
        },
    });

    // Bulk reject contacts mutation
    const bulkRejectContacts = useMutation({
        mutationFn: async (contactIds: number[]) => {
            await client.put(`/contacts/guest/reject`,
                { contactIds },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: `${selectedContactIds.length} contact(s) rejected successfully` },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setSelectedContactIds([]);
            setIsSelectAllChecked(false);
        },
        onError: (error: any) => {
            console.error("Bulk reject failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Failed to reject contacts. Please try again." },
                type: "failure",
            });
        },
    });

    // Individual accept contact mutation
    const acceptContact = useMutation({
        mutationFn: async (contactId: number) => {
            await client.put(`/contacts/guest/accept`,
                { contactIds: [contactId] },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Contact accepted successfully" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setActiveMenuIndex(null);
        },
        onError: (error: any) => {
            console.error("Accept failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Failed to accept contact. Please try again." },
                type: "failure",
            });
        },
    });

    // Individual reject contact mutation
    const rejectContact = useMutation({
        mutationFn: async (contactId: number) => {
            await client.put(`/contacts/guest/reject`,
                { contactIds: [contactId] },
                {
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Contact rejected successfully" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setActiveMenuIndex(null);
        },
        onError: (error: any) => {
            console.error("Reject failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Failed to reject contact. Please try again." },
                type: "failure",
            });
        },
    });

    // Bulk delete contacts mutation (for accepted contacts)
    const bulkDeleteContacts = useMutation({
        mutationFn: async (contactIds: number[]) => {
            await client.delete(`/contacts/guest/bulk`,
                {
                    data: { contactIds },
                    headers: {
                        Authorization: getBearerToken()
                    }
                });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: `${selectedContactIds.length} contact(s) deleted successfully` },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            setSelectedContactIds([]);
            setIsSelectAllChecked(false);
        },
        onError: (error: any) => {
            console.error("Bulk delete failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Failed to delete contacts. Please try again." },
                type: "failure",
            });
        },
    });

    const filteredContacts = contacts?.filter((contact) => {
        const fullName = `${contact?.firstName} ${contact?.lastName}`.toLowerCase();
        const search = searchText.toLowerCase();

        return ((
            fullName.includes(search) ||
            contact?.emailAddress?.toLowerCase().includes(search) ||
            contact?.company?.toLowerCase().includes(search) ||
            contact?.jobTitle?.toLowerCase().includes(search)
        ) && tagFilters.every((tag) => contact?.tag.includes(capitalizeWords(tag) as TagValue)));
    });

    // Helper functions for bulk selection
    const handleSelectAll = () => {
        if (isSelectAllChecked) {
            setSelectedContactIds([]);
            setIsSelectAllChecked(false);
        } else {
            const allContactIds = filteredContacts?.map(contact => contact.contactId).filter(id => id !== undefined) as number[] || [];
            setSelectedContactIds(allContactIds);
            setIsSelectAllChecked(true);
        }
    };

    const handleContactSelect = (contactId: number) => {
        setSelectedContactIds(prev => {
            if (prev.includes(contactId)) {
                const newSelection = prev.filter(id => id !== contactId);
                if (newSelection.length === 0) {
                    setIsSelectAllChecked(false);
                }
                return newSelection;
            } else {
                const newSelection = [...prev, contactId];
                const allContactIds = filteredContacts?.map(contact => contact.contactId).filter(id => id !== undefined) as number[] || [];
                if (newSelection.length === allContactIds.length) {
                    setIsSelectAllChecked(true);
                }
                return newSelection;
            }
        });
    };

    const handleBulkAccept = () => {
        if (selectedContactIds.length > 0) {
            bulkAcceptContacts.mutate(selectedContactIds);
        }
    };

    const handleBulkReject = () => {
        if (selectedContactIds.length > 0) {
            bulkRejectContacts.mutate(selectedContactIds);
        }
    };



    return (
        <div className="flex-1 flex relative flex-col pt-[2.69rem] px-[5%]">
            <Modal isOpen={introMessageModalOpened}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="flex items-end gap-[1rem] text-[0.875rem] text-headertext font-medium">
                                Introductory message & notes
                            </div>
                            <img
                                onClick={() => { setIntroMessageModalOpened(false); setSelectedContact(undefined) }}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col gap-[0.5rem] px-[0.94rem] pt-[0.94rem] pb-[1.19rem]">
                            <div className="flex justify-between items-end">
                                <div className="text-[0.85rem] text-headertext font-semibold">From {selectedContact?.firstName}</div>
                                <div className="text-[0.75rem] text-subtext font-medium">{selectedContact?.created ? format(new Date(selectedContact?.created as string), "MMM d, yyyy") : ""}</div>
                            </div>

                            <div className="text-[0.83rem] font-semibold text-subtext">
                                {selectedContact?.introductoryMessage ? `"${selectedContact?.introductoryMessage}"` : "No introductory message"}
                            </div>

                            <div className="text-[0.85rem] text-headertext font-semibold mt-2">Notes</div>
                            <div className="flex flex-col gap-[0.8rem] h-[12rem] overflow-y-scroll">
                                {
                                    selectedContact?.notes?.map((note, index) => {
                                        if (noteEditIndex !== index) {
                                            // If this note is marked for deletion, show confirmation UI
                                            if (noteToDelete === note.noteId) {
                                                return (
                                                    <div key={index} className="bg-red-50 flex flex-col px-[1rem] py-[0.5rem] border border-red-300 rounded-[0.625rem]">
                                                        <div className="text-[0.85rem] font-semibold text-red-600 mb-1">Delete this note?</div>
                                                        <div className="text-[0.75rem] text-red-500 mb-2">This action cannot be undone.</div>
                                                        <div className="flex gap-[0.5rem] justify-end">
                                                            <AppButton widthClass="px-[1rem] text-sm" heightClass="py-[0.4rem]"
                                                                onClick={() => setNoteToDelete(null)} alt
                                                                text="Cancel"
                                                            />
                                                            <AppButton widthClass="px-[1rem] text-sm" heightClass="py-[0.4rem]"
                                                                onClick={() => noteDelete.mutate(note.noteId as unknown as number)} 
                                                                text="Delete"
                                                            />
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            
                                            // Normal note display
                                            return (
                                                <div key={index} className="bg-transparent flex justify-between items-center px-[1rem] py-[0.5rem] border border-outline rounded-[0.625rem]">
                                                    <div className="flex flex-col truncate gap-[0.25rem]">
                                                        <div className="text-[0.85rem] font-semibold text-headertext">{note.note}</div>
                                                        <div className="text-[0.7rem] font-medium text-subtext">{note?.created ? format(new Date(note?.created as string), "MMM d, yyyy, h:mm a") : ""}</div>
                                                    </div>
                                                    <div className="flex gap-[0.5rem] text-[1rem]">
                                                        <BiEditAlt onClick={() => { setNoteEditIndex(index) }} className="cursor-pointer text-headertext" />
                                                        <TbTrash 
                                                            onClick={() => setNoteToDelete(note.noteId || null)} 
                                                            className="cursor-pointer text-red-500" 
                                                        />
                                                    </div>
                                                </div>
                                            );
                                        }

                                        return (
                                            <div key={index} className="flex flex-col gap-[0.5rem]">
                                                <div className="bg-grey px-[1rem] py-[0.5rem] mt-[0.8125rem] border border-outline rounded-[0.625rem]">
                                                    <div className="text-[0.85rem] text-headertext font-semibold mb-1">Note</div>
                                                    <textarea
                                                        placeholder={"Enter message"}
                                                        value={note?.note}
                                                        onChange={(e) => {
                                                            const contactCopy = JSON.parse(JSON.stringify(selectedContact)) as RowData
                                                            const currentNote = contactCopy.notes?.find((n) => n.noteId == note.noteId)
                                                            if (currentNote?.note != undefined) currentNote.note = e.target.value
                                                            setSelectedContact(contactCopy)
                                                        }}
                                                        rows={2}
                                                        className="outline-none w-full text-subtext bg-transparent font-semibold placeholder-placeholder text-[0.8125rem] resize-none"
                                                    />
                                                </div>
                                                <div className="flex">
                                                    <AppButton onClick={() => {
                                                        updateNote.mutate({ contactId: selectedContact!.contactId!, note: note.note, noteId: note!.noteId! });
                                                    }} heightClass="py-[0.4rem]" widthClass="text-sm font-medium px-[1rem]" text="Save" />
                                                </div>
                                            </div>
                                        )
                                    })
                                }
                            </div>
                            {
                                isCreateNoteMode ?
                                    <div className="flex flex-col gap-[0.5rem]">
                                        <div className="bg-grey px-[1rem] py-[0.5rem]  border border-outline rounded-[0.625rem]">
                                            <div className="text-[0.85rem] text-headertext font-semibold mb-1">Note</div>
                                            <textarea
                                                placeholder={"Enter notes"}
                                                value={newNote}
                                                onChange={(e) => { setNewNote(e.target.value) }}
                                                rows={2}
                                                className="outline-none w-full text-subtext bg-transparent font-semibold placeholder-placeholder text-[0.8125rem] resize-none"
                                            />
                                        </div>
                                        <div className="flex gap-[0.5rem]">
                                            <AppButton onClick={() => {
                                                setIsCreateNoteMode(false);
                                            }} heightClass="py-[0.4rem]" widthClass="text-sm font-medium px-[1rem]" alt text="Cancel" />
                                            <AppButton onClick={() => {
                                                createNote.mutate({ contactId: selectedContact!.contactId!, note: newNote });
                                            }} heightClass="py-[0.4rem]" widthClass="text-sm font-medium px-[1rem]" text="Save" />
                                        </div>
                                    </div>
                                    :
                                    <div className="flex mt-2">
                                        <AppButton onClick={() => {
                                            setIsCreateNoteMode(true);
                                        }} heightClass="py-[0.4rem]" widthClass="text-sm font-medium px-[1rem]" text="New Note" />
                                    </div>
                            }
                            <AppButton loading={createNote.isPending || updateNote.isPending} onClick={() => {
                                if (newNote) {
                                    createNote.mutate({ contactId: selectedContact!.contactId!, note: newNote });
                                }
                                selectedContact?.notes?.forEach((note) => {
                                    if (note.note) {
                                        updateNote.mutate({ contactId: selectedContact!.contactId!, note: note.note, noteId: note!.noteId! })
                                    }
                                })

                                setIntroMessageModalOpened(false);
                                setSelectedContact(undefined)
                                queryClient.invalidateQueries({ queryKey: ["contacts"] });
                            }} widthClass="text-[0.9rem] mt-[1.5rem]" text="Done" />
                        </div>
                    </div>
                </div>
            </Modal>
            <Modal isOpen={mobileContactModalOpened}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] pt-[0.94rem] pr-[0.69rem] items-center justify-end rounded-t-[0.625rem]">
                            <img
                                onClick={() => {
                                    setMobileContactModalOpened(false);
                                    if (JSON.stringify(currentSelectedTags) != JSON.stringify(selectedContact?.tag)) {
                                        tagUpdate.mutate(selectedContact?.contactId as number)
                                    }
                                    setSelectedContact(undefined);

                                }}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>
                        <div className="flex pl-[0.94rem] font-semibold text-black text-[0.9rem] pr-[0.69rem] pb-[0.25rem] items-center justify-between rounded-t-[0.625rem]">
                            {selectedContact?.firstName} {selectedContact?.lastName}
                        </div>
                        <div className="flex pl-[0.94rem] pr-[2rem] pb-[0.69rem] items-center justify-between">
                            <div className="flex flex-col font-medium text-placeholder text-[0.85rem]">
                                <div>{selectedContact?.jobTitle}</div>
                                <div>{selectedContact?.company}</div>
                                <div>{selectedContact?.phoneNumber}</div>
                                <div>{selectedContact?.emailAddress}</div>
                            </div>

                            <HiDownload onClick={() => { generateVCFInternal(selectedContact) }} className="hover:text-headertext text-placeholder" size={"1.4rem"} />
                        </div>

                        <div
                            className={`flex pl-[0.94rem] pr-[0.69rem] pb-[0.5rem] text-xs gap-[0.25rem] items-center`}
                        >
                            <div className="relative">
                                {
                                    (mobileTagSelectOpened) &&
                                    <div className="absolute z-[2] left-0 translate-x-[1.25rem]">
                                        <div className="w-[16rem] p-[1rem] pt-[0.5rem] shadow-lg bg-white rounded-[0.625rem] flex flex-col gap-[0.5rem]">
                                            <div className="flex justify-between">
                                                <div className="flex items-center gap-[0.5rem]">
                                                    <div className="w-[2.5rem] shrink-0 h-[2.5rem] bg-outline/50 flex justify-center items-center rounded-full">
                                                        <FaTags color="black" size={"1rem"} />
                                                    </div>

                                                    <div className="flex flex-col">
                                                        <div className="text-headertext font-bold">Tags</div>
                                                        <div className="text-placeholder font-semibold">Add a maximum of 2 tags</div>
                                                    </div>
                                                </div>

                                                <div className="self-start">
                                                    <BsX onClick={() => {
                                                        setMobileTagSelectOpened(false);
                                                    }} className="cursor-pointer" color="black" size={"1.2rem"} />
                                                </div>
                                            </div>

                                            <TagSelectInput
                                                maxSelectable={2}
                                                value={currentSelectedTags}
                                                options={TAGVALUE_OPTIONS}
                                                onChange={(selected) => {
                                                    setCurrentSelectedTags(selected);
                                                    console.log("Selected tags:", selected)
                                                }}
                                            />
                                        </div>
                                    </div>
                                }
                                <BiPlusCircle onClick={() => { setMobileTagSelectOpened(true); setCurrentSelectedTags(selectedContact?.tag as TagValue[]) }} color={colors.placeholder} size={"1.2rem"} />
                            </div>
                            {currentSelectedTags?.map((tag, index) => (
                                <TagComponent key={index} name={tag as TagValue} />
                            ))}
                        </div>

                        <div className="flex flex-col pl-[0.94rem] border-t border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] rounded-t-[0.625rem]">
                            <div className="flex items-end gap-[1rem] text-[0.875rem] text-headertext font-medium">
                                Introductory message From {selectedContact?.firstName}
                            </div>
                            <div className="text-[0.75rem] text-subtext font-medium">{selectedContact?.created ? format(new Date(selectedContact?.created as string), "MMM d, yyyy") : ""}</div>
                        </div>

                        <div className="flex flex-col gap-[0.5rem] px-[0.94rem] pb-[1.19rem]">

                            <div className="text-[0.83rem] font-semibold text-subtext">
                                {selectedContact?.introductoryMessage ? `"${selectedContact?.introductoryMessage}"` : "No introductory message"}
                            </div>

                            <div className="h-[12rem] overflow-y-scroll">
                                {
                                    selectedContact?.notes?.map((note, index) => (
                                        <div key={index} className="bg-grey px-[1rem] py-[0.5rem] mt-[0.8125rem] border border-outline rounded-[0.625rem]">
                                            <div className="text-[0.85rem] text-headertext font-semibold mb-1">Note</div>
                                            <textarea
                                                placeholder={"Enter message"}
                                                value={note?.note}
                                                onChange={(e) => {
                                                    const contactCopy = JSON.parse(JSON.stringify(selectedContact)) as RowData
                                                    const currentNote = contactCopy.notes?.find((n) => n.noteId == note.noteId)
                                                    if (currentNote?.note != undefined) currentNote.note = e.target.value
                                                    setSelectedContact(contactCopy)
                                                }}
                                                rows={4}
                                                className="outline-none w-full text-subtext bg-transparent font-semibold placeholder-placeholder text-[0.8125rem] resize-none"
                                            />
                                        </div>
                                    ))
                                }

                                <div className="bg-grey px-[1rem] py-[0.5rem] mt-[0.8125rem] border border-outline rounded-[0.625rem]">
                                    <div className="text-[0.85rem] text-headertext font-semibold mb-1">Note</div>
                                    <textarea
                                        placeholder={"Enter notes"}
                                        value={newNote}
                                        onChange={(e) => { setNewNote(e.target.value) }}
                                        rows={4}
                                        className="outline-none w-full text-subtext bg-transparent font-semibold placeholder-placeholder text-[0.8125rem] resize-none"
                                    />
                                </div>
                            </div>

                            <AppButton loading={createNote.isPending || updateNote.isPending} onClick={() => {
                                if (newNote) {
                                    createNote.mutate({ contactId: selectedContact!.contactId!, note: newNote });
                                }
                                selectedContact?.notes?.forEach((note) => {
                                    if (note.note) {
                                        updateNote.mutate({ contactId: selectedContact!.contactId!, note: note.note, noteId: note!.noteId! })
                                    }
                                })

                                setMobileContactModalOpened(false);
                                setSelectedContact(undefined)
                            }} widthClass="text-[0.9rem] mt-[1.5rem]" text="Done" />
                        </div>
                    </div>
                </div>
            </Modal>
            <Modal isOpen={deleteContactModalOpened}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="flex items-end gap-[1rem] text-[0.875rem] text-headertext font-medium">
                                Delete contact
                            </div>
                            <img
                                onClick={() => setDeleteContactModalOpened(false)}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.19rem]">
                            <div className="text-[0.875rem] text-placeholder">
                                <div className="text-subtext">
                                    Are you sure you want to delete {selectedContact?.firstName}'s contact? This action cannot be reversed
                                </div>
                            </div>

                            <div className="flex gap-[0.75rem]">
                                <div className="flex w-full mt-[1.14rem]">
                                    <AppButton loading={contactDelete.isPending} alt onClick={() => { setDeleteContactModalOpened(false); setSelectedContact(undefined) }} widthClass="flex-1" text="Cancel" />
                                </div>
                                <div className="flex w-full gap-[0.65rem] mt-[1.14rem]">
                                    <AppButton loading={contactDelete.isPending} onClick={() => { contactDelete.mutate(selectedContact?.contactId as number) }} widthClass="flex-1" text="Delete" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>
            <div className="flex -md:flex-col md:items-center w-full bg-white pb-[1rem]">
                <div className="text-[1rem] md:text-[1.125rem] font-semibold text-headertext">Contacts ({filteredContacts?.length || 0})</div>
                <div className="flex md:ml-auto gap-[0.4rem] -md:mt-[1.5rem] md:gap-[0.25rem] items-center py-[0.65rem] md:p-[0.65rem] md:border border-b border-outline md:rounded-[0.725rem]">
                    <BiSearchAlt className="text-[1.3rem] text-subtext md:text-black md:text-[0.85rem]" />
                    <input value={searchText} onChange={(e) => { setSearchText(e.target.value) }} placeholder="Search" className="appearance-none font-medium outline-none text-[1rem] md:text-[0.85rem] placeholder-subtext md:placeholder-placeholder" />
                </div>

                <div className="flex md:hidden w-full gap-[1rem] mt-[0.75rem] pb-[1rem] items-center overflow-x-auto">
                    {
                        TAGS.map((tag, index) => (
                            <div key={index} onClick={() => {
                                console.log(tagFilters, tag.value)
                                if (tagFilters.includes(tag.value as TagValue)) {
                                    setTagFilters(tagFilters.filter((t) => t != tag.value))
                                } else {
                                    setTagFilters([...tagFilters, tag.value as TagValue])
                                }
                            }} className="flex gap-[0.5rem] border border-outline shadow rounded-lg items-center px-[0.65rem] py-[0.4rem]">
                                <div className={`flex w-3 h-3 rounded-full border ${tag.borderClass} ${tagFilters.includes(tag.value as TagValue) ? tag.bgClass : ''}`} />
                                <div className={`text-[0.9rem] text-headertext font-medium`}>
                                    {tag.text}
                                </div>
                            </div>
                        ))
                    }
                </div>

                <div className="relative">
                    {
                        tagFilterOpened &&
                        <div className="absolute z-[2] right-0 translate-x-[1.25rem]">
                            <div className="w-[20rem] text-xs p-[1rem] pt-[0.5rem] shadow-lg bg-white rounded-[0.625rem] flex flex-col gap-[0.5rem]">
                                <div className="flex justify-between">
                                    <div className="flex items-center gap-[0.5rem]">
                                        <div className="flex flex-col">
                                            <div className="text-headertext font-bold">Filter by tag</div>
                                            <div className="text-placeholder font-semibold">Select a maximum of 2 tags</div>
                                        </div>
                                    </div>

                                    <div className="self-start">
                                        <BsX onClick={() => {
                                            setTagFilterOpened(false);
                                        }} className="cursor-pointer" color="black" size={"1.2rem"} />
                                    </div>
                                </div>

                                <TagSelectInput
                                    maxSelectable={2}
                                    value={tagFilters}
                                    options={TAGVALUE_OPTIONS}
                                    onChange={(selected) => {
                                        setTagFilters(selected);
                                        console.log("Active tag filters:", selected)
                                    }}
                                />
                            </div>
                        </div>
                    }
                    <BiFilter data-tooltip-id={"tagFilter"} onClick={() => { setTagFilterOpened(true); }} className="-md:hidden ml-[1rem] cursor-pointer" color="black" size={"1.5rem"} />
                    <Tooltip style={{ background: "white", boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }} id={"tagFilter"}>
                        <div className={`font-semibold text-[0.75rem] text-subheading bg-white`}>
                            Tag Filter
                        </div>
                    </Tooltip>
                </div>
            </div>
            <div className="-md:hidden text-xs flex gap-[0.5rem] ml-[1rem] items-center ml-auto">
                {tagFilters.length > 0 && "Active tag filters:"}
                {tagFilters.map((tag, index) => (
                    <TagComponent key={index} name={tag} handleXClick={() => { setTagFilters((filters) => filters.filter((filter) => filter !== tag)) }} />
                ))}
            </div>

            {/* Bulk Action Buttons */}
            {selectedContactIds.length > 0 && (
                <div className="flex items-center gap-[0.75rem] px-[1rem] py-[0.75rem] bg-blue-50 border border-blue-200 rounded-lg mb-[1rem]">
                    <div className="text-[0.875rem] font-medium text-headertext">
                        {selectedContactIds.length} contact{selectedContactIds.length > 1 ? 's' : ''} selected
                    </div>
                    <div className="flex gap-[0.5rem]">
                        <AppButton
                            onClick={handleBulkAccept}
                            loading={bulkAcceptContacts.isPending}
                            widthClass="px-[1rem] text-sm"
                            heightClass="py-[0.4rem]"
                            text="Accept Selected"
                        />
                        <AppButton
                            onClick={handleBulkReject}
                            loading={bulkRejectContacts.isPending}
                            widthClass="px-[1rem] text-sm"
                            heightClass="py-[0.4rem]"
                            alt
                            text="Reject Selected"
                        />
                        <AppButton
                            onClick={() => {
                                setSelectedContactIds([]);
                                setIsSelectAllChecked(false);
                            }}
                            widthClass="px-[1rem] text-sm"
                            heightClass="py-[0.4rem]"
                            alt
                            text="Clear Selection"
                        />
                    </div>
                </div>
            )}
            <div className="relative flex flex-1 overflow-y-auto">
                <div className="absolute flex w-full h-full">
                    <div className="flex-[3] pt-[1.13rem] overflow-y-auto">
                        <div className="flex flex-col  pb-20">
                            {/* Table Headers */}
                            <div className="flex -md:hidden sticky gap-[1rem] top-0 bg-white z-[1] w-full h-[3.63rem] border-y border-outline items-center text-[0.75rem] text-subtext font-bold">
                                {tableHeaders.map((tableHeader, index) => (
                                    <div
                                        key={index}
                                        className={index === 0 ? "ml-[3.75rem] flex items-center" : ""}
                                        style={{ flex: columnSizeRatios[index] }}
                                    >
                                        {index === 0 ? (
                                            <div
                                                onClick={handleSelectAll}
                                                className="cursor-pointer flex items-center justify-center w-[1.2rem] h-[1.2rem]"
                                            >
                                                {isSelectAllChecked ? (
                                                    <BsCheck2Square className="text-blue-600" size="1rem" />
                                                ) : (
                                                    <BsSquare className="text-gray-400" size="1rem" />
                                                )}
                                            </div>
                                        ) : (
                                            tableHeader
                                        )}
                                    </div>
                                ))}
                            </div>

                            {/* Table Rows */}
                            {
                                filteredContacts && filteredContacts?.length > 0 ?
                                    filteredContacts?.map((row: RowData, rowIndex: number) => (
                                        <div key={rowIndex}
                                            onClick={() => { setActiveMenuIndex(null) }}
                                            className="flex -md:hidden gap-[1rem] font-medium w-full h-[3.63rem] items-center text-[0.75rem] text-subtext border-b border-outline">
                                            {/* Handle each column explicitly */}
                                            {tableKeys.map((key, colIndex) => {
                                                // Type assertion for key to be one of RowData's keys

                                                // Special handling for select checkbox
                                                if (key === "select") {
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className="ml-[3.75rem] flex items-center justify-center"
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    if (row.contactId) {
                                                                        handleContactSelect(row.contactId);
                                                                    }
                                                                }}
                                                                className="cursor-pointer flex items-center justify-center w-[1.2rem] h-[1.2rem]"
                                                            >
                                                                {selectedContactIds.includes(row.contactId || 0) ? (
                                                                    <BsCheck2Square className="text-blue-600" size="1rem" />
                                                                ) : (
                                                                    <BsSquare className="text-gray-400" size="1rem" />
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                } else if (key === "guest") {
                                                    const guestName = `${row["firstName"]} ${row["lastName"]}`; // Access guest_name value safely
                                                    const sub = `${row["jobTitle"]}, ${row["company"]}`; // Access guest_name value safely
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className={"flex flex-col min-w-0"}
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div className="font-bold text-black truncate text-[0.8rem]">{guestName}</div>
                                                            <div className="truncate text-[0.75rem] text-subtext">{sub}</div>
                                                        </div>
                                                    );
                                                } else if (key == "contact-info") {
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className={"flex flex-col min-w-0"}
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div className="font-semibold text-black truncate text-[0.8rem]">{row["phoneNumber"]}</div>
                                                            <div className="truncate text-[0.75rem] text-subtext">{row["emailAddress"]}</div>
                                                        </div>
                                                    );
                                                } else if (key == "created") {
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className={"flex flex-col min-w-0"}
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div className="truncate text-[0.8rem]">{format(new Date(row["created"]), 'MMMM d, yyyy')}</div>
                                                        </div>
                                                    );
                                                } else if (key == "introductoryMessage") {
                                                    return (
                                                        <div key={colIndex} onClick={async () => {
                                                            setSelectedContact(row); await getContactData(row.contactId as number); setIntroMessageModalOpened(true);
                                                        }} style={{ flex: columnSizeRatios[colIndex] }} className="text-orange font-bold cursor-pointer">
                                                            View
                                                        </div>
                                                    );
                                                } else if (key == "tag") {
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className={`flex gap-[0.25rem] items-center`}
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div className="relative">
                                                                {
                                                                    (tagSelectOpened && selectedContact == row) &&
                                                                    <div className="absolute z-[2] right-0 translate-x-[1.25rem]">
                                                                        <div className="w-[20rem] p-[1rem] pt-[0.5rem] shadow-lg bg-white rounded-[0.625rem] flex flex-col gap-[0.5rem]">
                                                                            <div className="flex justify-between">
                                                                                <div className="flex items-center gap-[0.5rem]">
                                                                                    <div className="w-[2.5rem] shrink-0 h-[2.5rem] bg-outline/50 flex justify-center items-center rounded-full">
                                                                                        <FaTags color="black" size={"1rem"} />
                                                                                    </div>

                                                                                    <div className="flex flex-col">
                                                                                        <div className="text-headertext font-bold">Tags</div>
                                                                                        <div className="text-placeholder font-semibold">Add a maximum of 2 tags</div>
                                                                                    </div>
                                                                                </div>

                                                                                <div className="self-start">
                                                                                    <BsX onClick={() => {
                                                                                        setTagSelectOpened(false);
                                                                                        setSelectedContact(undefined);
                                                                                        if (JSON.stringify(currentSelectedTags) != JSON.stringify(row["tag"])) {
                                                                                            tagUpdate.mutate(row.contactId as number)
                                                                                        }
                                                                                    }} className="cursor-pointer" color="black" size={"1.2rem"} />
                                                                                </div>
                                                                            </div>

                                                                            <TagSelectInput
                                                                                maxSelectable={2}
                                                                                value={currentSelectedTags}
                                                                                options={TAGVALUE_OPTIONS}
                                                                                onChange={(selected) => {
                                                                                    setCurrentSelectedTags(selected);
                                                                                    console.log("Selected tags:", selected)
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                }
                                                                <BiPlusCircle onClick={() => { setTagSelectOpened(true); setSelectedContact(row); setCurrentSelectedTags(row["tag"]) }} color={colors.placeholder} size={"1.2rem"} />
                                                            </div>
                                                            {row["tag"]?.map((tag, index) => (
                                                                <TagComponent key={index} name={tag as TagValue} />
                                                            )) || "N/A"}
                                                        </div>
                                                    );
                                                } else if (key == "buttons") {
                                                    return (
                                                        <div
                                                            key={colIndex}
                                                            className={`flex gap-[0.25rem] items-center`}
                                                            style={{ flex: columnSizeRatios[colIndex] }}
                                                        >
                                                            <div className="relative cursor-pointer">
                                                                <img
                                                                    className="w-[0.94rem] h-[0.94rem]"
                                                                    src="/ellipsis.svg"
                                                                    onClick={(e) => { e.stopPropagation(); setActiveMenuIndex((prev) => (prev === rowIndex ? null : rowIndex)) }}
                                                                />

                                                                {activeMenuIndex === rowIndex && (
                                                                    <div className="absolute border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem] bg-white rounded-md z-[2]">
                                                                        <div
                                                                            onClick={() => {
                                                                                if (row.contactId) {
                                                                                    acceptContact.mutate(row.contactId);
                                                                                }
                                                                            }}
                                                                            className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-teal-700">

                                                                            Accept
                                                                        </div>
                                                                        <div
                                                                            onClick={() => {
                                                                                if (row.contactId) {
                                                                                    rejectContact.mutate(row.contactId);
                                                                                }
                                                                            }}
                                                                            className="text-[0.875rem] text-danger cursor-pointer duration-200 hover:font-semibold">

                                                                            Reject
                                                                        </div>
                                                                        <div className="h-[2px] bg-gray-300" />
                                                                        <div
                                                                            onClick={() => {
                                                                                // TODO: Add your download logic here
                                                                                console.log("Download clicked", row);
                                                                                generateVCFInternal(row);
                                                                                setActiveMenuIndex(null);
                                                                            }}
                                                                            className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext">

                                                                            Download
                                                                        </div>

                                                                        <div
                                                                            onClick={() => {
                                                                                setSelectedContact(row);
                                                                                setDeleteContactModalOpened(true);
                                                                                setActiveMenuIndex(null);
                                                                            }}
                                                                            className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger">

                                                                            Remove contact
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                    );
                                                }

                                                // Default rendering for other keys
                                                return (
                                                    <div
                                                        key={colIndex}
                                                        className={`truncate min-w-0`}
                                                        style={{ flex: columnSizeRatios[colIndex] }}
                                                    >
                                                        {row[key as keyof RowDataSoft] || "N/A"} {/* Access other properties safely */}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    ))
                                    :
                                    <div className="flex justify-center w-full h-[6rem] items-center text-[0.9rem] text-subtext md:border-t md:border-outline">
                                        No contacts
                                    </div>
                            }
                            {/* Mobile Select All Button */}
                            <div className="md:hidden flex items-center justify-between p-[0.75rem] mb-[0.5rem]">
                                <div
                                    onClick={handleSelectAll}
                                    className="cursor-pointer flex items-center gap-[0.5rem]"
                                >
                                    {isSelectAllChecked ? (
                                        <BsCheck2Square className="text-blue-600" size="1.2rem" />
                                    ) : (
                                        <BsSquare className="text-gray-400" size="1.2rem" />
                                    )}
                                    <span className="text-[0.875rem] font-medium text-headertext">
                                        {isSelectAllChecked ? 'Deselect All' : 'Select All'}
                                    </span>
                                </div>
                                {selectedContactIds.length > 0 && (
                                    <span className="text-[0.75rem] text-subtext">
                                        {selectedContactIds.length} selected
                                    </span>
                                )}
                            </div>

                            {/* Mobile Bulk Action Bar */}
                            {selectedContactIds.length > 0 && (
                                <div className="md:hidden flex flex-col gap-[0.5rem] p-[1rem] bg-blue-50 border border-blue-200 rounded-lg mb-[1rem]">
                                    <div className="text-[0.875rem] font-medium text-headertext mb-[0.5rem]">
                                        {selectedContactIds.length} contact{selectedContactIds.length > 1 ? 's' : ''} selected
                                    </div>
                                    <div className="flex gap-[0.5rem]">
                                        <AppButton
                                            onClick={handleBulkAccept}
                                            loading={bulkAcceptContacts.isPending}
                                            widthClass="flex-1 text-sm"
                                            heightClass="py-[0.5rem]"
                                            text="Accept Selected"
                                        />
                                        <AppButton
                                            onClick={handleBulkReject}
                                            loading={bulkRejectContacts.isPending}
                                            widthClass="flex-1 text-sm"
                                            heightClass="py-[0.5rem]"
                                            alt
                                            text="Reject Selected"
                                        />
                                    </div>
                                    <AppButton
                                        onClick={() => {
                                            setSelectedContactIds([]);
                                            setIsSelectAllChecked(false);
                                        }}
                                        widthClass="w-full text-sm"
                                        heightClass="py-[0.4rem]"
                                        alt
                                        text="Clear Selection"
                                    />
                                </div>
                            )}

                            <div className="flex flex-col gap-[0.5rem]">
                                {
                                    filteredContacts && filteredContacts?.length > 0 &&
                                    filteredContacts?.map((row: RowData, rowIndex: number) => (
                                        <div key={rowIndex} className="flex items-center md:hidden w-full p-[0.75rem] rounded-xl shadow border border-outline">
                                            <div
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (row.contactId) {
                                                        handleContactSelect(row.contactId);
                                                    }
                                                }}
                                                className="cursor-pointer flex items-center justify-center w-[1.2rem] h-[1.2rem] mr-[0.75rem]"
                                            >
                                                {selectedContactIds.includes(row.contactId || 0) ? (
                                                    <BsCheck2Square className="text-blue-600" size="1rem" />
                                                ) : (
                                                    <BsSquare className="text-gray-400" size="1rem" />
                                                )}
                                            </div>

                                            <div
                                                onClick={async () => { setSelectedContact(row); setCurrentSelectedTags(row.tag); await getContactData(row.contactId as number); setMobileContactModalOpened(true); }}
                                                className="flex flex-col min-w-0 flex-1 cursor-pointer"
                                            >
                                                <div className="text-[0.9rem] font-medium truncate">{row.firstName} {row.lastName}</div>
                                                <div className="text-[0.85rem] text-placeholder font-medium truncate">
                                                    {row.jobTitle || row.company
                                                        ? `${row.jobTitle ?? ""}${row.company ? `, ${row.company}` : ""}`
                                                        : "N/A"}
                                                </div>
                                            </div>

                                            <div className="flex gap-[0.2rem] ml-auto">
                                                {
                                                    row.tag?.map((t, index) => (
                                                        <div key={index} className={`rounded-full w-2 h-2 ${TAG_DICT[t].bgClass}`} />
                                                    ))
                                                }
                                            </div>

                                            <div
                                                className={`flex gap-[0.25rem] ml-[1rem] items-center`}
                                            >
                                                <div className="relative cursor-pointer">
                                                    <FaEllipsisV
                                                        onClick={(e) => { e.stopPropagation(); setActiveMenuIndex((prev) => (prev === rowIndex ? null : rowIndex)) }}
                                                        className="text-placeholder text-[1rem]" />

                                                    {activeMenuIndex === rowIndex && (
                                                        <div className="absolute border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem] bg-white rounded-md z-[2]">
                                                            <div
                                                                onClick={() => {
                                                                    if (row.contactId) {
                                                                        acceptContact.mutate(row.contactId);
                                                                    }
                                                                }}
                                                                className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-teal-700">

                                                                Accept
                                                            </div>

                                                            <div
                                                                onClick={() => {
                                                                    if (row.contactId) {
                                                                        rejectContact.mutate(row.contactId);
                                                                    }
                                                                }}
                                                                className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger">

                                                                Reject
                                                            </div>

                                                            <div className="h-[2px] bg-gray-300" />

                                                            <div
                                                                onClick={() => {
                                                                    generateVCFInternal(row);
                                                                    setActiveMenuIndex(null);
                                                                }}
                                                                className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext">

                                                                Download
                                                            </div>

                                                            <div
                                                                onClick={() => {
                                                                    setSelectedContact(row);
                                                                    setDeleteContactModalOpened(true);
                                                                    setActiveMenuIndex(null);
                                                                }}
                                                                className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger">

                                                                Remove contact
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                    </div>

                    {/* <div className="flex-[1] sticky top-0 border border-outline shadow w-32">
                        {
                            selectedContact ?
                                <div className="flex flex-col relative items-center overflow-y-auto w-full h-full">
                                    <div className="flex flex-col gap-[1rem] absolute items-center w-full h-full">
                                       

                                        <div>
                                            <BsPersonCircle className="w-[10rem] h-[10rem]" />
                                        </div>

                                        <div className="flex flex-col w-full gap-[0.63rem] px-[1.5rem]">
                                            {
                                                tableKeys.map((key, index) => {
                                                    if (key == "name") {
                                                        return (
                                                            <div key={index} className="flex gap-[0.5rem] items-center justify-center text-center">
                                                                <div className="text-subtext font-medium capitalize text-[0.8125rem]">{tableHeaders[index]}:</div>
                                                                <div className="text-headertext font-semibold text-[0.83rem]">{selectedContact.firstName} {selectedContact.lastName}</div>
                                                            </div>
                                                        )
                                                    } else {
                                                        return (
                                                            <div key={index} className="flex gap-[0.5rem] items-center justify-center text-center">
                                                                <div className="text-subtext font-medium capitalize text-[0.8125rem]">{tableHeaders[index]}:</div>
                                                                <div className="text-headertext font-semibold text-[0.83rem]">{selectedContact[key as keyof RowData] || "N/A"}</div>
                                                            </div>
                                                        )
                                                    }
                                                })
                                            }
                                        </div>

                                        <div className="w-full px-[1.5rem] mt-5">
                                            <AppTextArea placeholder="Type notes here...." rows={8} />
                                        </div>

                                        <div className="flex pb-[3rem] px-[1.5rem] w-full justify-center items-center shadow-lg gap-[0.62rem]">
                                            <AppButton onClick={() => { setSelectedContact(undefined) }} alt widthClass="flex-1 text-[0.9375rem]" heightClass="h-[2.4rem]" text="Close" />
                                            <AppButton widthClass="flex-1 text-[0.9375rem]" heightClass="h-[2.4rem]" text="Update" />
                                        </div>
                                    </div>
                                </div>
                                :
                                <div className="flex items-center text-[0.9rem] justify-center w-full h-full text-placeholder font-semibold">
                                    No contact selected
                                </div>
                        }
                    </div> */}
                </div>
            </div>
        </div>
    );
}
