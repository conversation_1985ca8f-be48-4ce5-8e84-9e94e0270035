"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import AppTextInput from "@/components/AppTextInput";
import AppButton from "@/components/AppButton";
import { useMutation } from "@tanstack/react-query";
import { useNotification } from "@/contexts/NotificationProvider";
import { getBearerToken, getUserId } from "@/app/tokenStorage";
import client from "@/api/client";
import { fetchMetaData } from "@/utils/fetchMetaData";
import { SocialProof } from "../../../../../interfaces";

export default function AddSocialProof() {
    const router = useRouter();
    const [errorMessage, setErrorMessage] = useState("");
    const { showNotification } = useNotification();
    const [socialProofs, setSocialProofs] = useState<(SocialProof & {
        titleVisible?: boolean;
        isLoading?: boolean;
    })[]>([{ title: "", links: "", titleVisible: false, isLoading: false }]);

    const handleAddItem = () => {
        setSocialProofs([...socialProofs, { title: "", links: "", titleVisible: false, isLoading: false }]);
    };

    const handleChangeItem = (index: number, field: keyof SocialProof, value: string) => {
        const updatedItems = [...socialProofs];
        updatedItems[index][field] = value;
        setSocialProofs(updatedItems);
    };

    const handleRemoveItem = (index: number) => {
        setSocialProofs(socialProofs.filter((_, i) => i !== index));
    };

    const showTitleField = (index: number) => {
        const updatedItems = [...socialProofs];
        updatedItems[index].titleVisible = true;
        setSocialProofs(updatedItems);
    };

    const fetchTitle = async (index: number) => {
        const current = socialProofs[index];
        if (!current.links.trim()) {
            setErrorMessage(`Link #${index + 1} is required to fetch metadata.`);
            return;
        }
    
        // Set loading true
        setSocialProofs((prev) =>
            prev.map((item, i) =>
                i === index ? { ...item, isLoading: true } : item
            )
        );
    
        try {
            const { title } = await fetchMetaData(current.links);
    
            setSocialProofs((prev) =>
                prev.map((item, i) =>
                    i === index
                        ? {
                              ...item,
                              title: title || "",
                              titleVisible: true,
                              isLoading: false,
                          }
                        : item
                )
            );
        } catch (e) {
            setErrorMessage(`Failed to fetch metadata for link #${index + 1}`);
            setSocialProofs((prev) =>
                prev.map((item, i) =>
                    i === index ? { ...item, isLoading: false } : item
                )
            );
        }
    };

    const handleSave = () => {
        const validItems = [];
        for (let i = 0; i < socialProofs.length; i++) {
            const item = socialProofs[i];
            if (!item.links.trim()) {
                setErrorMessage(`Link #${i + 1} is required.`);
                return;
            }
            validItems.push({
                links: item.links.trim(),
                title: item.title?.trim() || "",
            });
        }
        mutation.mutate(validItems);
    };

    const mutation = useMutation({
        mutationFn: async (values: SocialProof[]) => {
            setErrorMessage("");
            const userId = getUserId();
            return client.post(
                "/social/bulk",
                values.map((val) => ({
                    ...val,
                    usersId: userId ? parseInt(userId) : userId,
                })),
                {
                    headers: {
                        Authorization: getBearerToken(),
                    },
                }
            );
        },
        onSuccess: (response) => {
            console.log("Social proofs Added:", response.data);
            showNotification({
                renderObj: { midSection: "Social proofs added successfully" },
                type: "success",
            });
            router.push("/social-proof");
        },
        onError: (error: any) => {
            console.error("Social proofs addition failed:", error);
            setErrorMessage(
                error?.response?.data?.message ||
                "Social proofs addition failed. Please try again."
            );
        },
    });

    return (
        <div className="flex-1 flex relative flex-col pt-[1.19rem] px-[0.94rem] md:px-[4.38rem]">
            <GoBackButton />
            <div className="flex-1 pt-[0.69rem] flex flex-col items-center">
                <div className="md:w-[26.88rem] pb-[5rem]">
                    <div className="text-[1.375rem] font-medium">Add social proof link(s)</div>
                    <div className="mt-[0.5rem] text-subtext">
                        Showcase your establishment's social proof. Enter links to websites you've been mentioned.
                    </div>

                    {errorMessage && (
                        <div className="text-red-600 mt-3 text-sm">{errorMessage}</div>
                    )}

                    <div className="flex flex-col mt-[1.81rem] gap-[0.75rem]">
                        {socialProofs.map((item, index) => (
                            <div key={index} className="flex mb-4 items-start gap-2">
                                <div className="flex-1">
                                    {item.titleVisible && (
                                        <div className="">
                                            <AppTextInput
                                                value={item.title}
                                                label={`Title #${index + 1}`}
                                                placeholder="Enter title"
                                                onChange={(e) =>
                                                    handleChangeItem(
                                                        index,
                                                        "title",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                        </div>
                                    )}
                                    <AppTextInput
                                        value={item.links}
                                        label={`Link #${index + 1}`}
                                        placeholder="Enter link"
                                        onChange={(e) =>
                                            handleChangeItem(index, "links", e.target.value)
                                        }
                                    />
                                    

                                </div>
                                {socialProofs.length > 1 && (
                                    <button
                                        className="text-red-500 hover:text-red-700 ml-2"
                                        onClick={() => handleRemoveItem(index)}
                                    >
                                        ✖
                                    </button>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="mt-[0.94rem] flex items-center cursor-pointer" onClick={handleAddItem}>
                        <img src="/plus-orange.svg" className="w-[0.5rem] mx-[0.38rem] h-[0.5rem]" />
                        <div className="text-[0.8125rem] text-orange">Add new</div>
                    </div>

                    <AppButton widthClass="w-full mt-[1.88rem]" text="Save" onClick={handleSave} />
                </div>
            </div>
        </div>
    );
}
