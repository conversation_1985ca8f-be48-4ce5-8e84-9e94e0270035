"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import AppButton from "@/components/AppButton";
import { SocialProofCard } from "@/components/SocialProofCard";
import Modal from "@/components/Modal";
import AppTextInput from "@/components/AppTextInput";
import client from "@/api/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getBearerToken, getToken, getUserId } from "@/app/tokenStorage";
import { useNotification } from "@/contexts/NotificationProvider";
import { fetchMetaData } from "@/utils/fetchMetaData";

interface SocialProof {
    id: number;
    title: string;
    links: string;
}

export default function SocialProof() {
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedProofId, setSelectedProofId] = useState<number>(1);
    const [mode, setMode] = useState("edit");
    const [linkInput, setLinkInput] = useState("");
    const [titleInput, setTitleInput] = useState("");
    const { showNotification } = useNotification();
    const queryClient = useQueryClient()


    const { data: socialProofs, isLoading, error } = useQuery({
        queryKey: ["social-proofs"],
        queryFn: async () => {
            const userId = getUserId()
            const response = await client.get(`/social/${userId}`);
            // Assuming the API returns the templates in response.data.data
            return response.data.data
        },
    });

    // Handle changing ttitle for the selected social proof
    const handleUpdate = useMutation({
        mutationFn: async (values: SocialProof) => {
            const userId = getUserId();
            return client.put(
                `/social/${values.id}`,
                { ...values, usersId: parseInt(userId as string) },
                {
                    headers: {
                        Authorization: getBearerToken(),
                    },
                }
            );
        },
        onSuccess: (response) => {
            console.log("Social proof updated:", response.data);
            showNotification({
                renderObj: { midSection: "Social proof updated" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["social-proofs"] });
        },
        onError: (error: any) => {
            console.error("Social proof update failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Social proofs addition failed." },
                type: "failure",
            });

        },
    });

    // Handle removing the selected social proof
    const handleDelete = useMutation({
        mutationFn: async (values: { id: number }) => {
            return client.delete(
                `/social/${values.id}`,
                {
                    headers: {
                        Authorization: getBearerToken(),
                    },
                }
            );
        },
        onSuccess: (response) => {
            console.log("Social proof deleted:", response.data);
            showNotification({
                renderObj: { midSection: "Social proof deleted" },
                type: "success",
            });
            queryClient.invalidateQueries({ queryKey: ["social-proofs"] });
        },
        onError: (error: any) => {
            console.error("Social proof deletion failed:", error);
            showNotification({
                renderObj: { midSection: error?.response?.data?.message || error?.message || "Social proofs deletion failed." },
                type: "failure",
            });

        },
    });

    if (isLoading) {
        return (
            <div className="flex-1 px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[8.5rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        );
    }

    return (
        <>
            <Modal isOpen={isModalOpen}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="text-[0.875rem] text-headertext font-medium">
                                {mode == "edit" ? "Edit Link" : "Remove social proof"}
                            </div>
                            <img
                                onClick={() => setIsModalOpen(false)}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.19rem] gap-[1.25rem]">
                            {mode == "edit" ? (
                                <>
                                    <AppTextInput
                                        value={titleInput}
                                        label={`Title`}
                                        placeholder="Enter title"
                                        onChange={(e) => setTitleInput(e.target.value)}
                                    />
                                    <AppTextInput
                                        value={linkInput}
                                        label={`Link`}
                                        placeholder="Enter link"
                                        onChange={(e) => setLinkInput(e.target.value)}
                                    />
                                </>
                            ) : (
                                <div className="text-placeholder text-[0.905625rem]">
                                    Are you sure you want to delete this social proof? This action cannot be undone.
                                </div>
                            )}

                            <div className="flex w-full gap-[0.75rem]">
                                {
                                    mode == "remove" &&
                                    <div
                                        onClick={() => {
                                            setIsModalOpen(false);
                                        }}
                                        className="flex flex-1 cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center h-[3.13rem] rounded-[0.625rem]"
                                    >
                                        Cancel
                                    </div>
                                }
                                <div
                                    onClick={() => {
                                        if (mode === "edit") {
                                            handleUpdate.mutate({ id: selectedProofId, links: linkInput, title: titleInput });
                                        } else {
                                            handleDelete.mutate({ id: selectedProofId });
                                        }
                                        setIsModalOpen(false);
                                    }}
                                    className={`flex flex-1 cursor-pointer font-medium bg-black text-white border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]`}
                                >
                                    {mode === "edit" ? "Save" : "Remove"}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            {socialProofs?.length > 0 ? (
                <div className="flex-1 flex relative flex-col px-[1rem] md:px-[3.81rem]">
                    <AppButton
                        onClick={() => {
                            router.push("/social-proof/add");
                        }}
                        widthClass="w-[11.81rem] -md:hidden mt-[1.88rem] ml-auto"
                        text="Add social proof"
                    />
                    <div className="md:hidden flex items-center text-[1.125rem] justify-between font-medium">
                        <div>Social proof</div>
                        <img onClick={() => { router.push("/social-proof/add") }} className="md:hidden w-[1.5rem] h-[1.5rem]" src="/plus-small.svg" />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-[1.06rem] mt-[1.88rem] mx-[0.57rem]">
                        {socialProofs.map((proof: SocialProof, index: number) => (
                            <SocialProofCard
                                onEditPress={async () => {
                                    setSelectedProofId(proof.id);
                                    setLinkInput(proof.links); // Set the current link for editing
                                    const { title } = await fetchMetaData(proof.links)
                                    setTitleInput(proof.title || title);
                                    setMode("edit");
                                    setIsModalOpen(true);
                                }}
                                onRemovePress={() => {
                                    setSelectedProofId(proof.id);
                                    setMode("remove");
                                    setIsModalOpen(true);
                                }}
                                key={proof.id}
                                proof={proof}
                            />
                        ))}
                    </div>
                </div>
            ) : (
                <div className="flex-1 flex relative flex-col items-center justify-center">
                    <img src="/comment.svg" className="w-[2.5rem] h-[2.5rem]" />
                    <div className="mt-[0.75rem] font-medium">You have no social proofs yet</div>
                    <div className="mt-[0.56rem] text-[0.875rem] text-subtext w-[13.5rem] text-center">
                        Add links to websites & articles you've been mentioned in
                    </div>
                    <AppButton
                        onClick={() => {
                            router.push("/social-proof/add");
                        }}
                        widthClass="w-[11.81rem] mt-[1.19rem]"
                        text="Add social proof"
                    />
                </div>
            )}
        </>
    );
}
