"use client";

import client from "@/api/client";
import { getBearerToken, getToken, getUserId } from "@/app/tokenStorage";
import AppTextInput from "@/components/AppTextInput";
import LineChart from "@/components/charts/LineChart";
import FilterSelect from "@/components/FilterSelect";
import GeoDataTable from "@/components/GeoDataTable";
import LinkTrackingTable from "@/components/LinkTrackingTable";
import StatCard from "@/components/StatCard";
import flags from "@/utils/flags_mapping";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

const stats = [
    {
        key: "cardTaps",
        title: "Card Taps/Scans",
        value: "0",
        percentageChange: "+ 0%",
        periodDescription: "this month",
        ttText: "The total number of times the QR Code has been scanned or the NFC card has been tapped",
        ttTextWidthClass: "w-[17.02rem]"
    },
    {
        key: "totalSaves",
        title: "Total Saves",
        value: "0",
        percentageChange: "+ 0%",
        periodDescription: "this month",
        ttText: "Number of times guest saved users' contact information ",
        ttTextWidthClass: "w-[11.69rem]"
    },
    {
        key: "contactExchange",
        title: "Contact Exchanges",
        value: "0",
        percentageChange: "+ 0%",
        periodDescription: "this month",
        ttText: "Number of times contact details have been successfully exchanged.",
        ttTextWidthClass: "w-[17.02rem]"
    },
    {
        key: "interactionRate",
        title: "Interaction Rate",
        value: "0",
        percentageChange: "+ 0%",
        periodDescription: "this month",
        ttText: "The percentage of viewers interacting with any elements on the profile page. (e.g., links, save, exchange contacts etc.).",
        ttTextWidthClass: "w-[14.78rem]"
    },
];

const linkTable = {
    Instagram: 0,
    X: 0,
    Linkedin: 0,
    Website: 0,
    "Social Proof": 0,
    Testimonials: 0,
};


const geoTableRows = [
    { flag_key: "united_states" as keyof typeof flags, label: "USA/NYC", value: "02 days ago" },
    { flag_key: "italy" as keyof typeof flags, label: "IT/Florence", value: "05 days ago" },
    { flag_key: "united_kingdom" as keyof typeof flags, label: "UK/Birmingham", value: "08 days ago" },
    { flag_key: "ghana" as keyof typeof flags, label: "GH/Accra", value: "12 days ago" },
    { flag_key: "nigeria" as keyof typeof flags, label: "NG/Lagos", value: "15 days ago" },
];

const filterOptions = ["Monthly", "Weekly", "Daily"];


export default function Dashboard() {
    const [selectedFilter, setSelectedFilter] = useState('Monthly');
    const [password, setPassword] = useState('');


    const router = useRouter()

    const { data: userMetrics, isLoading, error } = useQuery({
        queryKey: ["metrics"],
        queryFn: async () => {
            const response = await client.get(`/metrics/all/${getUserId()}`, {
                headers: {
                    Authorization: getBearerToken()
                }
            });
            // Assuming the API returns the templates in response.data.data
            return response.data.data
        },
    });

    return (
        <div className="flex flex-col">
            <div className="mt-[1.38rem] sm:mt-[2.69rem] mb-[2.25rem] font-semibold text-[1.125rem] text-headertext ml-[0.94rem] md:ml-[3.13rem]">Dashboard</div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-[0.5rem] mx-[0.94rem] md:ml-[4.5rem] md:mr-[4.31rem]">
                {stats.map((stat, index) => (
                    <StatCard
                        key={index}
                        ttId={`${index}`}
                        ttText={stat.ttText}
                        ttTextWidthClass={stat.ttTextWidthClass}
                        title={stat.title}
                        value={(userMetrics && userMetrics[stat.key as keyof typeof userMetrics]) || stat.value}
                        percentageChange={stat.percentageChange}
                        periodDescription={stat.periodDescription}
                    />
                ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-[0.69rem] mt-[0.75rem] mx-[0.94rem] md:ml-[4.5rem] md:mr-[4.31rem]">
                <LinkTrackingTable entries={userMetrics ? { ...userMetrics["linkTracking"], "testimonials": 0, "social proof": 0 } : linkTable} />
                <GeoDataTable rows={geoTableRows} />
            </div>

            <div className="mt-[1.75rem] mx-[0.94rem] md:ml-[4.5rem] md:mr-[4.31rem] pb-[4.42rem]">
                <div className="rounded-[0.9375rem] overflow-hidden pb-[1.5rem] cursor-pointer border border-outline">

                    <div className="flex items-center justify-between px-[0.94rem] md:pl-[1.62rem] md:pr-[2.33rem] pt-[1.21rem] pb-[1.37rem]">
                        <div className="flex items-center gap-[0.56rem]">
                            <div className="text-headertext">Profile views</div>
                            <img className="w-[0.88rem] h-[0.88rem]" src="/info-green.svg" alt="Info" />
                        </div>

                        <FilterSelect options={filterOptions} defaultOption="Monthly" onChange={(value) => { setSelectedFilter(value) }} />

                    </div>

                    <div className="h-[26rem] w-full overflow-x-auto px-[0.94rem] md:pl-[1.62rem] md:pr-[2.33rem]">
                        <div className="h-full min-w-[30rem] px-[0.94rem] md:pl-[1.62rem] md:pr-[2.33rem]">
                            <LineChart />
                        </div>
                    </div>

                </div>
            </div>

        </div>
    );
}
