"use client";

import AppTextInput from "@/components/AppTextInput";
import BCard from "@/components/BCard";
import { useRouter } from "next/navigation";
import { useState } from "react";

const orderTabs = ["current", "past"];

export default function Support() {
    const [selectedFilter, setSelectedFilter] = useState('Monthly');
    const [password, setPassword] = useState('');
    const [activeOrderTab, setActiveOrderTab] = useState(orderTabs[0]);
    const [cartItems, setCartItems] = useState([
        {
            id: 1,
            firstName: "Cina",
            lastName: "Lane",
            quantity: 1,
            price: 10.00,
        },
        // Add more items as needed
    ]);

    const router = useRouter()

    return (
        <div className="flex flex-col -lg:w-full lg:ml-[7.81rem] pb-[5rem]">
            <div className="font-medium capitalize text-[1.125rem] lg:text-[1.375rem] text-black mt-[0.06rem]">
                Support
            </div>

            <div className="flex flex-wrap gap-[1.19rem] mt-[1.63rem]">
                <div className="w-full lg:w-[17.63rem] rounded-[0.6875rem] bg-grey border pb-[1.56rem] border-outline">
                    <div className="flex items-center justify-center bg-white ml-[0.88rem] mt-[1.44rem] rounded-[0.5rem] w-[3.5rem] h-[3.5rem]">
                        <img src="/user-headset.svg" className="w-[1.5rem] h-[1.5rem]" />
                    </div>

                    <div className="mt-[4.19rem] mx-[0.44rem] text-[1.125rem] text-headertext font-semibold">
                        Email sales
                    </div>
                    <div className="mt-[0.31rem] mx-[0.44rem] text-placeholder font-medium">
                        Reach out to our sales team
                    </div>
                    <div className="mt-[1rem] cursor-pointer mx-[0.44rem] text-headertext underline font-medium">
                        <EMAIL>
                    </div>

                </div>

                <div className="w-full lg:w-[17.63rem] rounded-[0.6875rem] bg-grey border pb-[1.56rem] border-outline">
                    <div className="flex items-center justify-center bg-white ml-[0.88rem] mt-[1.44rem] rounded-[0.5rem] w-[3.5rem] h-[3.5rem]">
                        <img src="/ticket.svg" className="w-[1.5rem] h-[1.5rem]" />
                    </div>

                    <div className="mt-[4.19rem] mx-[0.44rem] text-[1.125rem] text-headertext font-semibold">
                        Email support
                    </div>
                    <div className="mt-[0.31rem] mx-[0.44rem] text-placeholder font-medium">
                        Send us an email for assistance
                    </div>
                    <div className="mt-[1rem] cursor-pointer mx-[0.44rem] text-headertext underline font-medium">
                        <EMAIL>
                    </div>

                </div>

                <div className="w-full lg:w-[17.63rem] rounded-[0.6875rem] bg-grey border pb-[1rem] border-outline">
                    <div className="flex items-center justify-center bg-white ml-[0.88rem] mt-[1.44rem] rounded-[0.5rem] w-[3.5rem] h-[3.5rem]">
                        <img src="/phone-flip.svg" className="w-[1.5rem] h-[1.5rem]" />
                    </div>

                    <div className="mt-[4.19rem] mx-[0.44rem] text-[1.125rem] text-headertext font-semibold">
                        Request call back
                    </div>
                    <div className="mt-[0.31rem] mx-[0.44rem] text-placeholder font-medium">
                        We'll call you. Provide your number.
                    </div>
                    <div className="flex">
                        <div className="mt-[1rem] cursor-pointer text-[0.8125rem] bg-black rounded-[0.625rem] px-[0.8rem] py-[0.3rem] mx-[0.44rem] text-white font-medium">
                            Request Callback
                        </div>
                    </div>

                </div>
            </div>


        </div>
    );
}
