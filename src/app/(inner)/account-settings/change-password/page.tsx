"use client";

import { useRouter } from "next/navigation";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { useNotification } from "@/contexts/NotificationProvider";
import { getDecodedToken, getToken } from "@/app/tokenStorage";

const validationSchema = Yup.object({
    oldPassword: Yup.string().required("Old password is required"),
    newPassword: Yup.string()
        .min(8, "Password must be at least 8 characters")
        .required("New password is required"),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword")], "Passwords must match")
        .required("Confirm password is required"),
});

export default function ChangePassword() {
    const router = useRouter();
    const { showNotification } = useNotification()


    const mutation = useMutation({
        mutationFn: async (values: { oldPassword: string; newPassword: string }) => {
            return client.post("/settings/change_password", { ...values, emailAddress: (getDecodedToken().emailAddress) }, {
                headers: {
                    Authorization: `Bearer ${getToken()}`
                }
            });
        },
        onSuccess: () => {
            showNotification({ renderObj: { midSection: "Password change successful" }, type: "success" })
            router.replace("/login");
        },
        onError: (error: any) => {
            showNotification({ renderObj: { midSection: error?.response?.data?.message || error?.message }, type: "failure" })
            console.error("Error:", error);
        },
    });

    return (
        <div className="flex flex-col -md:w-full md:ml-[7.81rem]">
            <div className="font-medium capitalize text-[1.125rem] md:text-[1.375rem] text-black mt-[0.06rem]">
                Change Password
            </div>

            <Formik
                initialValues={{ oldPassword: "", newPassword: "", confirmPassword: "" }}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    mutation.mutate(
                        { oldPassword: values.oldPassword, newPassword: values.newPassword },
                        { onSettled: () => setSubmitting(false) }
                    );
                }}
            >
                {({ isSubmitting }) => (
                    <Form className="flex flex-col w-full md:w-[26.88rem] mt-[1.75rem] gap-[1.13rem]">
                        <FormikAppTextInput name="oldPassword" label="Enter old password" placeholder="Old password" type="password" />
                        <FormikAppTextInput name="newPassword" label="Create new password" placeholder="Create password" type="password" />
                        <FormikAppTextInput name="confirmPassword" label="Confirm new password" placeholder="Confirm password" type="password" />

                        <FormikAppSubmit text="Reset password" widthClass="w-full mt-[1.88rem]" disabled={isSubmitting || mutation.isPending} />
                    </Form>
                )}
            </Formik>
        </div>
    );
}
