"use client";

import BCard from "@/components/BCard";
import AccountSettingsContext from "@/contexts/AccountSettingsContext";
import { useRouter } from "next/navigation";
import { useContext, useState, useEffect } from "react";
import { useMediaQuery } from "usehooks-ts";
import { useQuery } from "@tanstack/react-query";
import client from "@/api/client";
import { getBearerToken, getUserId } from "@/app/tokenStorage";
import { CARD_THEMES } from "../../../../../constants";
import { format } from "date-fns";

// Interface for the API response card data
interface ApiCardData {
    id: string;
    userId: number;
    completed: boolean;
    path: string;
    cardTemplateId: number;
    firstName?: string;
    lastName?: string;
    colour?: string;
    logo?: string;
    role?: string;
    logoHeight?: number;
    logoWidth?: number;
    created: string;
    [key: string]: any; // For any other properties that might be in the API response
}

// Interface for our transformed card items
interface OrderCardItem {
    id: string;
    firstName: string;
    lastName: string;
    quantity: number;
    jobTitle: string;
    orderDate: string;
    deliveryDate: string;
    status: string;
    colour?: string;
    total?: number;
    completed: boolean;
    cardTemplateId: number;
    logoHeight?: number;
    logoWidth?: number;
    logo?: string;
}

const orderTabs = ["current", "past"];

export default function AccountSettings() {
    const [selectedFilter, setSelectedFilter] = useState('Monthly');
    const [password, setPassword] = useState('');
    const { showOrderSettingsMobile, setShowOrderSettingsMobile } = useContext(AccountSettingsContext)
    const [activeOrderTab, setActiveOrderTab] = useState(orderTabs[0]);
    const [cartItems, setCartItems] = useState<OrderCardItem[]>([]);

    const router = useRouter()
    const isMobile = useMediaQuery('(max-width: 1023px)');

    const { data: userCards, isLoading, error } = useQuery({
        queryKey: ["user-cards"],
        queryFn: async () => {
            const userId = getUserId();
            const response = await client.get(`/cards/user/${userId}`, {
                headers: {
                    Authorization: `${getBearerToken()}`
                }
            });
            return response.data.data as ApiCardData[];
        },
    });

    useEffect(() => {
        if (userCards) {
            // Transform API data to match cartItems structure
            const transformedCards = userCards.map((card: ApiCardData): OrderCardItem => ({
                ...card,
                id: card.id,
                firstName: card.firstName || "",
                lastName: card.lastName || "",
                quantity: card.quantity,
                total: card.total,
                jobTitle: card.role || "Systems Analyst",
                orderDate: card.orderDate,
                deliveryDate: card.deliveryDate, // Using created date as fallback
                status: card.completed ? "Completed" : "Processing",
                colour: card.colour,
                completed: card.completed,
                cardTemplateId: card.cardTemplateId,
                logo: card.logo,
                logoHeight: card.logoHeight,
                logoWidth: card.logoWidth
            }));

            setCartItems(transformedCards);
        }
    }, [userCards]);

    // Filter cards based on active tab
    const filteredCartItems = cartItems.filter((item: OrderCardItem) =>
        activeOrderTab === 'current' ? !item.deliveryDate : item.deliveryDate
    );

    return (
        <div className={`${(isMobile && !showOrderSettingsMobile) ? 'hidden' : ''} md:flex flex-col -md:w-full  md:ml-[5rem]`}>
            <div className="flex justify-between -md:items-center md:gap-[8.38rem]">
                <div className="flex bg-grey items-center h-[3rem] px-[0.18rem] rounded-[0.625rem]">
                    {
                        orderTabs.map((orderTab, index) => (
                            <div key={index} onClick={() => { setActiveOrderTab(orderTab) }} className={`${activeOrderTab === orderTab ? 'bg-white rounded-[0.5rem]' : ''} -md:font-medium capitalize cursor-pointer text-headertext text-[0.75rem] h-[2.63rem] px-[0.81rem] py-[0.94rem]`}>
                                {orderTab} Orders
                            </div>
                        ))
                    }
                </div>

                <img onClick={() => { router.push("/select-card-template") }} className="md:hidden w-[1.5rem] h-[1.5rem]" src="/plus-small.svg" />

                <div onClick={() => { router.push("/select-card-template") }} className="hidden md:flex cursor-pointer w-[10.56rem] h-[2.75rem] items-center justify-center rounded-[0.625rem] text-headertext border font-medium border-black">Order new card</div>
            </div>

            <div className="font-bold capitalize md:text-[1.375rem] text-black mt-[1.31rem]">
                {activeOrderTab} Orders
            </div>

            <div className="mt-[1.3rem]">
                {filteredCartItems.length > 0 ? (
                    filteredCartItems.map((item: OrderCardItem) => {
                        const itemColorObj = CARD_THEMES.find((c) => c.id === parseInt(item?.colour as string)) || CARD_THEMES[0];

                        console.log(item)

                        return (
                            <div key={item.id} className="relative flex border border-outline rounded-[0.625rem] py-[0.65rem] pl-[0.57rem] h-[4.9rem] md:h-[5.5rem] mb-[0.75rem]">
                                <div className="absolute z-[2]">
                                    <div className="absolute z-[3] font-bold -left-1 -top-1 flex items-center justify-center text-white bg-headertext text-xs w-5 h-5 rounded-full">{item.quantity}</div>
                                    <BCard
                                        scale={isMobile ? 0.33 : 0.4}
                                        firstName={item.firstName}
                                        lastName={item.lastName}
                                        jobTitle={item.jobTitle}
                                        logoImageUrl={item.logo}
                                        logoHeight={item.logoHeight as number}
                                        logoWidth={item.logoWidth as number}
                                        bgColor={itemColorObj.hex}
                                        textColor={itemColorObj.textColorHex}
                                    />
                                </div>

                                <div className="flex-1 flex items-center justify-between pl-[7rem] md:pl-[8.47rem]">
                                    <div className="">
                                        <div className="text-headertext font-medium">{item.firstName} {item.lastName}</div>
                                        <div className="text-subtext font-medium">GH₵{item?.total?.toFixed(2)}</div>
                                    </div>
                                    {
                                        activeOrderTab === orderTabs[0] ?
                                            <div className="text-info text-[0.8rem] font-medium mr-[0.5rem] md:mr-[1.81rem]">
                                                To be delivered
                                            </div>
                                            :
                                            <div className="text-info text-[0.8rem] font-medium mr-[0.5rem] md:mr-[1.81rem] text-right">
                                                <div className="text-placeholder">Ordered: {item?.orderDate ? format(new Date(item?.orderDate as string), "MMM d, yyyy") : ""}</div>
                                                <div className="text-placeholder">Delivered: 25/04/2024</div>
                                            </div>
                                    }
                                </div>
                            </div>
                        )
                    })
                ) : (
                    <div className="text-center text-subtext font-medium mt-[2rem]">
                        {activeOrderTab === 'current' ? 'No current orders found.' : 'No past orders found.'}
                    </div>
                )}
            </div>


        </div>
    );
}
