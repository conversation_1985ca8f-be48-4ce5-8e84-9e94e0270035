"use client";

import client from "@/api/client";
import { getDecodedToken, getToken, getUserId } from "@/app/tokenStorage";
import Modal from "@/components/Modal";
import { useNotification } from "@/contexts/NotificationProvider";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function ProfileStatus() {
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isActive, setIsActive] = useState(true);
    const { showNotification } = useNotification()

    const handleDeactivate = () => {
        setIsActive(false);
        setIsModalOpen(false);
    };

    const handleReactivate = () => {
        setIsActive(true);
        setIsModalOpen(false);
    };

    const mutation = useMutation({
        mutationFn: async (values: { active: boolean }) => {
            const userId = getUserId()
            return client.post(`/settings/change_status/${userId}`, { ...values }, {
                headers: {
                    Authorization: `Bearer ${getToken()}`
                }
            });
        },
        onSuccess: (data, variables) => {
            setIsActive(variables.active);
            setIsModalOpen(false);
            showNotification({ renderObj: { midSection: "Status change successful" }, type: "success" })
        },
        onError: (error: any) => {
            setIsModalOpen(false);
            showNotification({ renderObj: { midSection: error?.response?.data?.message || error?.message }, type: "failure" })
            console.error("Error:", error);
        },
    });

    return (
        <div className="flex flex-col md:ml-[8rem]">
            {/* Modal */}
            <Modal isOpen={isModalOpen}>
                <div className="flex flex-col justify-center px-[0.5rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white md:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="text-[0.875rem] text-headertext font-medium">
                                {isActive ? "Deactivate profile" : "Reactivate profile"}
                            </div>
                            <img
                                onClick={() => setIsModalOpen(false)}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.56rem] gap-[1.75rem]">
                            <div className="text-placeholder text-[0.905625rem]">
                                Are you sure you want to {isActive ? "de" : "re"}activate your profile? This action can be undone when needed.
                            </div>

                            <div className="flex w-full gap-[0.75rem]">
                                <div
                                    onClick={() => setIsModalOpen(false)}
                                    className="flex flex-1 cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center h-[3.13rem] rounded-[0.625rem]"
                                >
                                    Cancel
                                </div>
                                <div
                                    onClick={isActive ? () => mutation.mutate({ active: false }) : () => mutation.mutate({ active: true })}
                                    className={`flex flex-1 cursor-pointer font-medium bg-black text-white border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]`}
                                >
                                    {isActive ? "Deactivate" : "Reactivate"}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            {/* Profile Status */}
            <div className="font-medium capitalize text-[1.375rem] text-black mt-[0.06rem]">
                Account status
            </div>
            <div className="text-subheading mt-[0.5rem]">
                Your profile is currently{" "}
                <span className={isActive ? "text-green" : "text-danger"}>
                    {isActive ? "Active" : "Inactive"}
                </span>
            </div>

            {/* Deactivate/Reactivate Button */}
            <div
                onClick={() => setIsModalOpen(true)}
                className={`flex mt-[2.06rem] border ${isActive ? "border-danger text-danger" : "border-green text-green"
                    } w-[12.31rem] mt-[1.88rem] cursor-pointer items-center justify-center h-[3.13rem] rounded-[0.625rem] font-medium`}
            >
                {isActive ? "Deactivate profile" : "Reactivate profile"}
            </div>
        </div>
    );
}
