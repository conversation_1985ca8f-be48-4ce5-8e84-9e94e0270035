"use client";

import InnerTopNav from "@/components/navigation/InnerTopNav";
import { getBearerToken, getToken, getUserId } from "../tokenStorage";
import { redirect } from "next/navigation";
import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import { useNotification } from "@/contexts/NotificationProvider";
import { useAuthStore } from "@/store/authStore";


export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    const [errorMessage, setErrorMessage] = useState("")
    const { showNotification } = useNotification()
    const { setUser } = useAuthStore()

    const mutation = useMutation({
        mutationFn: async () => {
            setErrorMessage("");
            return client.post("/profile/portal", { userId: parseInt(getUserId() as string) });
        },
        onSuccess: async (response) => {
            setUser(response.data?.data)
            console.log("Profile retrieval successful:", response.data);

        },
        onError: (error: any) => {
            console.error("Profile retrieval failed:", error);
            // showNotification({ renderObj: { midSection: "Profile retrieval failed" }, type: "failure" })
            // setErrorMessage(
            //     error?.response?.data?.message || error?.message || "Profile retrieval failed"
            // );
        },
    });

    useEffect(() => {
        if (!getToken()) {
            redirect("/login")
        } else {
            mutation.mutate()
        }
    }, [])



    if (!getToken()) {
        return <div></div>
    }

    return (
        <>
            <InnerTopNav />
            {children}
        </>
    );
}
