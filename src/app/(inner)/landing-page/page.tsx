"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { HiLocationMarker } from 'react-icons/hi';
import colors from "../../../../themePalette";
import ContactNav from "@/components/ContactNav";
import { useQuery } from "@tanstack/react-query";
import client from "@/api/client";
import { getDecodedToken, getToken, getUserId } from "@/app/tokenStorage";
import { BsGoogle, BsInstagram, BsLinkedin, BsShare, BsTiktok, BsTwitterX, BsWhatsapp, BsYoutube } from "react-icons/bs";
import { FaFacebook } from "react-icons/fa";
import { useAuthStore } from "@/store/authStore";
import { MdAccountCircle } from "react-icons/md";
import { BiShare, BiShareAlt } from "react-icons/bi";
import { Tooltip } from "react-tooltip";
import { expandMapsUrl } from "@/utils/expandMapsUrl";
import { Address, formatAddress, getAdrDetailsFromSharedMapsUrl, getEmbedUrlFromGoogleMapsUrl } from "@/utils/helpers";

export default function LandingPage() {
    const [isOpen, setIsOpen] = useState(false);
    const [loadingMap, setLoadingMap] = useState(false)
    const [mapEmbedUrl, setMapEmbedUrl] = useState("")
    const [address, setAddress] = useState<Address>()
    const router = useRouter();

    const icons = {
        "twitter": <BsTwitterX />,
        "linkedin": <BsLinkedin />,
        "instagram": <BsInstagram />,
        "facebook": <FaFacebook />,
        "tiktok": <BsTiktok />,
        "whatsapp": <BsWhatsapp />,
        "youtube": <BsYoutube />,
        "google": <BsGoogle />
    }

    // const { data: userProfile, isLoading, error } = useQuery({
    //     queryKey: ["user_profile"],
    //     queryFn: async () => {
    //         const response = await client.post(`/profile/portal`, {
    //             userId: 1, location: JSON.stringify(location)
    //         }, {
    //             headers: {
    //                 Authorization: `Bearer ${getToken()}`
    //             }
    //         });
    //         // alert(JSON.stringify(response.data))
    //         // Assuming the API returns the templates in response.data.data
    //         return response.data.data
    //     },
    // });
    const [tooltipText, setTooltipText] = useState("Click to copy profile link");

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText("https://beyond-delta.vercel.app/u/contact/" + getUserId());
            setTooltipText("Copied!");
            setTimeout(() => setTooltipText("Click to copy profile link"), 2000);
        } catch (err) {
            setTooltipText("Failed to copy");
            setTimeout(() => setTooltipText("Click to copy profile link"), 2000);
        }
    };

    const { user } = useAuthStore()

    useEffect(() => {
        setLoadingMap(true);
        (async () => {
            if (user?.location) {
                try {
                    const expandedUrl = await expandMapsUrl(user?.location);
                    if (expandedUrl) {
                        console.log("Expanded Url: ", expandedUrl)
                        const embedUrl = getEmbedUrlFromGoogleMapsUrl(expandedUrl)
                        console.log("Embed Url: ", embedUrl)
                        setMapEmbedUrl(embedUrl)
                    }
                    console.log("Expanded No Url: ", expandedUrl)
                } catch (error) {
                    console.error(error)
                }
            }
        })();
        (async () => {
            if (user?.location) {
                const address_ = await getAdrDetailsFromSharedMapsUrl(user?.location)
                setAddress(address_ as Address)
                console.log("Address:", address_)
            }
        })();
        setLoadingMap(false);
    }, [user])

    console.log(user)
    return (
        <div className="flex relative flex-col md:mt-[5.69rem] w-full max-w-[52.81rem] mx-auto items-center">


            <div
                className="h-[14.38rem] w-full bg-cover"
                style={{ backgroundImage: user?.coverPhoto ? `url('${user?.coverPhoto}')` : "url('/contact-bg.svg')" }}
            ></div>

            <div className="flex flex-col w-full absolute items-center h-screen">

                {
                    user?.profileImage ?
                        <img
                            className="absolute z-[2] border-[0.15rem] border-white rounded-full w-[6rem] h-[6rem] mt-[4.3rem] object-cover"
                            src={user.profileImage}
                        />
                        :
                        <MdAccountCircle size={"6rem"} className="absolute z-[2] bg-white text-headertext/90 border-[0.15rem] border-white rounded-full w-[6rem] h-[6rem] mt-[4.3rem]"
                        />
                }

                <div className="px-[1.19rem] w-full">
                    <div className="relative flex flex-col items-center mt-[7.5rem] pt-[3.48rem] text-center w-full pb-[1.69rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div data-tooltip-id={"share"}
                            onClick={handleCopy}
                            className="absolute top-[1.31rem] left-[1.31rem] flex justify-center items-center w-[3.13rem] h-[3.13rem] rounded-full bg-grey"
                        >
                            <BiShareAlt className="cursor-pointer w-[1.25rem] h-[1.25rem]" />
                        </div>
                        <Tooltip style={{ background: "white", boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }} id={"share"}>
                            <div className={`font-semibold text-[0.75rem] text-subheading bg-white`}>
                                {tooltipText}
                            </div>
                        </Tooltip>
                        <div
                            onClick={() => router.push('/landing-page-settings')}
                            className="absolute top-[1.31rem] right-[1.31rem] flex justify-center items-center w-[3.13rem] h-[3.13rem] rounded-full bg-grey"
                        >
                            <img
                                className="cursor-pointer w-[1.25rem] h-[1.25rem]"
                                src="/settings.svg"
                            />
                        </div>
                        <div className="text-headertext font-semibold text-[1.125rem]">
                            {user?.firstName} {user?.lastName}
                        </div>
                        <div className="text-placeholder font-medium mt-[0.19rem] text-[0.9375rem]">
                            {user?.role || "[Role not set]"}
                            {/* ,<br />
                            {user?.company || "[Company not set]"} */}
                        </div>
                        <div className="flex gap-[0.31rem] items-center text-subtext mt-[1.31rem]">
                            <img
                                src="/small-phone.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{user?.phoneNumber || "[Phone number not set]"}</div>
                        </div>
                        <div className="flex gap-[0.38rem] items-center text-subtext mt-[0.69rem]">
                            <img
                                src="/small-envelope.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{user?.emailAddress}</div>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col w-full mt-[0.75rem] gap-[0.75rem]">

                    <div className="flex flex-col gap-[0.38rem] mx-[1.19rem] px-[0.63rem] pt-[0.75rem] pb-[0.65rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div className="text-headertext">Social networks</div>
                        <div className="flex gap-[0.63rem]">
                            {
                                (user?.links && Object.keys(user?.links).length !== 0) ? Object.entries(user?.links).map(([key, value], index) => (
                                    (
                                        <>
                                            {user?.links[key] &&
                                                <div key={index} onClick={() => router.push(value as string)} className="cursor-pointer h-[2.54rem] w-[2.54rem] bg-outline/50 rounded-full flex items-center justify-center">
                                                    {icons[key as keyof typeof icons]}
                                                </div>}
                                        </>
                                    )
                                    // <img key={index} onClick={() => router.push(value)} src={`/${key}.svg`} className="h-[2.54rem] w-[2.54rem]" />
                                )) :
                                    <div className="cursor-pointer text-[0.85rem] font-medium text-subtext h-[2.54rem] flex items-center justify-center">
                                        No links added
                                    </div>
                            }
                        </div>
                    </div>
                    <div className="flex flex-col mx-[1.19rem] gap-[0.75rem]">
                        {/* <ContactNav
                        onClick={() => router.push("/product-catalog")}
                        text="Product catalog"
                    />
                    <ContactNav
                        onClick={() => router.push("/service-catalog")}
                        text="Service catalog" /> */}

                        {
                            user?.hasTestimonial && <ContactNav
                                onClick={() => router.push("/testimonials")}
                                text="Testimonials"
                            />
                        }
                        {
                            user?.hasSocialProof && <ContactNav
                                onClick={() => router.push("/social-proof")}
                                text="Social proof"
                            />
                        }
                        {
                            user?.website && <ContactNav onClick={() => { router.push(user?.website?.startsWith("http") ? user?.website : "https://" + user?.website) }} text="Website" hasArrow={false} />
                        }
                    </div>

                    {
                        !loadingMap && (mapEmbedUrl ?
                            <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                                <div>
                                    <div className="text-headertext">Location (Google Maps)</div>
                                    <div className="flex items-center gap-1 mt-1">
                                        <HiLocationMarker size={24} color={colors.headertext} />
                                        {
                                            address &&
                                            <span className={"text-sm text-headertext"}>{formatAddress(address as Address)}</span>
                                        }
                                    </div>
                                </div>
                                <div className="h-[11.19rem] w-full">
                                    <iframe
                                        className="w-full h-full rounded-[0.625rem]"
                                        src={mapEmbedUrl}
                                        allowFullScreen
                                        loading="lazy"
                                    ></iframe>
                                </div>
                            </div>
                            :
                            <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                                <div>
                                    <div className="text-headertext">Location (Google Maps)</div>
                                    <div className="flex items-center gap-1 mt-1">
                                        <HiLocationMarker size={24} color={colors.headertext} />
                                        <div className="text-[0.85rem] font-semibold text-center text-red-500">
                                            Location not provided
                                        </div>
                                    </div>
                                </div>
                            </div>)
                    }

                    {
                        loadingMap &&
                        <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                            <div>
                                <div className="text-headertext">Location (Google Maps)</div>
                                <div className="flex items-center gap-1 mt-1">
                                    <HiLocationMarker size={24} color={colors.headertext} />
                                    <div className="text-[0.85rem] font-semibold text-center text-gray-500">
                                        Loading...
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    {/* <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div>
                            <div className="text-headertext">Location (Google Maps)</div>
                            <div className="flex items-center gap-1 mt-1">
                                <HiLocationMarker size={24} color={colors.headertext} />
                                <span className={"text-sm text-headertext"}>Accra, Ghana</span>
                            </div>
                        </div>
                        <div className="h-[11.19rem] w-full">
                            <iframe
                                className="w-full h-full rounded-[0.625rem]"
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.835434509374!2d144.9630579153163!3d-37.81410797975159!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad642af0f11fd81%3A0x5045675218ce6e0!2sFederation%20Square%2C%20Melbourne%20VIC%2C%20Australia!5e0!3m2!1sen!2sgh!4v1690288112336!5m2!1sen!2sgh"
                                allowFullScreen
                                loading="lazy"
                            ></iframe>
                        </div>
                    </div> */}

                    <div className="bg-white pt-[1.44rem] pb-[1.13rem] text-placeholder text-center text-[0.75rem]">
                        Copyright 2025 BeyondCard
                    </div>


                </div>

            </div>
        </div>
    );
}
