"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import AppTextInput from "@/components/AppTextInput";
import AppButton from "@/components/AppButton";
import AppTextArea from "@/components/AppTextArea";
import AppImageInput from "@/components/AppImageInput";
import AppSelectInput from "@/components/AppSelectInput";

export default function AddProduct() {
    const router = useRouter();
    const [image, setImage] = useState<File | null>(null);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setImage(e.target.files[0]);
        }
    };

    return (
        <div className="flex-1 flex relative flex-col pt-[1.69rem] px-[4.38rem]">
            <GoBackButton />
            <div className="flex-1 flex flex-col items-center">
                <div className="w-[26.88rem]">
                    <div className="text-[1.375rem] font-medium">Add product</div>
                    <div className="flex flex-col mt-[1.94rem] gap-[1.13rem]">
                        <AppImageInput label="Product Images" onImageChange={handleImageChange} />
                        <AppTextInput
                            label={`Product name`}
                            placeholder="Enter product name"
                        />
                        <AppTextInput
                            label={`Price`}
                            placeholder="GHS"
                            type="number"
                        />
                        <AppSelectInput
                            label="Select product category"
                            options={[
                                { value: "electronics", label: "Electronics" },
                                { value: "fashion", label: "Fashion" },
                                { value: "books", label: "Books" },
                            ]}
                        // value={selectedValue}
                        // onChange={handleChange}
                        />
                        <AppTextArea
                            label={`Product description`}
                            placeholder="Description"
                            rows={3}
                        />
                        <AppTextInput
                            label={`In stock`}
                            labelExtra="optional"
                            placeholder="10"
                            type="number"
                        />
                        <div className='flex items-center gap-[0.38rem]'>
                            <input type="checkbox" onChange={() => { }} checked={false} className='w-[0.81rem] cursor-pointer h-[0.81rem] accent-[#111111]' />
                            <div className="text-headertext text-[0.875rem]">Discount</div>
                        </div>

                    </div>

                    <AppButton
                        widthClass="w-full mt-[1.88rem] mb-[3rem]"
                        text="Add product"
                        disabled
                        onClick={() => { }}
                    />
                </div>
            </div>
        </div>
    );
}
