"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import GoBackButton from "@/components/GoBackButton";
import AppTextInput from "@/components/AppTextInput";
import AppButton from "@/components/AppButton";
import AppTextArea from "@/components/AppTextArea";

export default function Setup() {
    const router = useRouter();
    const [socialProofs, setSocialProofs] = useState<string[]>([""]);

    const handleAddLink = () => {
        setSocialProofs([...socialProofs, ""]);
    };

    const handleChangeLink = (index: number, value: string) => {
        const updatedLinks = [...socialProofs];
        updatedLinks[index] = value;
        setSocialProofs(updatedLinks);
    };

    const handleSave = () => {
        // Validate and save the social proofs
        const validLinks = socialProofs.filter(link => link.trim() !== "");
        router.push("/p-catalog/products")
        console.log("Saving links:", validLinks);
        // Perform your save logic here
    };

    return (
        <div className="flex-1 flex relative flex-col pt-[4rem] px-[4.38rem]">
            <div className="flex-1 flex flex-col items-center">
                <div className="w-[26.88rem]">
                    <div className="text-[1.375rem] font-medium">Set up your store</div>

                    <div className="flex flex-col mt-[1.94rem] gap-[1.13rem]">
                        <AppTextInput
                            label={`Store name`}
                            placeholder="Enter store name"
                        />
                        <AppTextInput
                            label={`Location`}
                            placeholder="Enter location"
                        />
                        <AppTextInput
                            label={`Store whatsapp name`}
                            placeholder="Enter store whatsapp name"
                        />
                        <AppTextArea
                            label={`Describe your business`}
                            placeholder="description"
                            rows={3}
                        />
                    </div>

                    <AppButton
                        widthClass="w-full mt-[1.88rem] mb-[3rem]"
                        text="Setup"
                        onClick={handleSave}
                    />
                </div>
            </div>
        </div>
    );
}
