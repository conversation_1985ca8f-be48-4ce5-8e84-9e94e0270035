"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import AppButton from "@/components/AppButton";
import { SocialProofCard } from "@/components/SocialProofCard";
import Modal from "@/components/Modal";
import AppTextInput from "@/components/AppTextInput";

const sProofs = [
  {
    imageSrc: `https://picsum.photos/800/600?random=1`,
    altText: "Award Achievement",
    title: "Awarded Ghana Product Manager of the Year, 2023.",
    source: "https://example.com",
  },
  {
    imageSrc: `https://picsum.photos/800/600?random=2`,
    altText: "Tech Summit",
    title: "Featured speaker at the African Tech Summit 2024.",
    source: "https://example.com",
  },
  {
    imageSrc: `https://picsum.photos/800/600?random=3`,
    altText: "Product Launches",
    title: "Led over 50 product launches with a 90% success rate.",
    source: "https://example.com",
  },
  {
    imageSrc: `https://picsum.photos/800/600?random=4`,
    altText: "Agile Certification",
    title: "Certified in Advanced Agile Product Management by PMI.",
    source: "https://example.com",
  },
  {
    imageSrc: `https://picsum.photos/800/600?random=5`,
    altText: "Client Rating",
    title: "Rated 4.9/5 by 150+ clients on consulting platforms.",
    source: "https://example.com",
  },
];

export default function PCatalog() {
  const router = useRouter();
  const [dashData, setDashData] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedProofIndex, setSelectedProofIndex] = useState<number>(0);
  const [mode, setMode] = useState("edit");
  const [linkInput, setLinkInput] = useState("");

  // Handle changing the link for the selected social proof
  const handleChangeLink = (index: number, newLink: string) => {
    const updatedProofs = [...dashData];
    updatedProofs[index].source = newLink;
    setDashData(updatedProofs);
  };

  // Handle removing the selected social proof
  const handleRemoveLink = (index: number) => {
    const updatedProofs = dashData.filter((_, idx) => idx !== index);
    setDashData(updatedProofs);
  };

  return (
    <>
      <Modal isOpen={isModalOpen}>
        <div className="flex flex-col justify-center px-[0.5rem] items-center w-screen h-screen bg-black/50">
          <div className="bg-white w-[28.75rem] rounded-[0.625rem]">
            <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
              <div className="text-[0.875rem] text-headertext font-medium">
                {mode == "edit" ? "Edit Link" : "Remove social proof"}
              </div>
              <img
                onClick={() => setIsModalOpen(false)}
                src="/cross-small.svg"
                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
              />
            </div>

            <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.19rem] gap-[1.25rem]">
              {mode == "edit" ? (
                <AppTextInput
                  value={linkInput}
                  label={`Link #${selectedProofIndex + 1}`}
                  placeholder="Enter link"
                  onChange={(e) => setLinkInput(e.target.value)}
                />
              ) : (
                <div className="text-placeholder text-[0.905625rem]">
                  Are you sure you want to delete this social proof? This action cannot be undone.
                </div>
              )}

              <div className="flex w-full gap-[0.75rem]">
                {
                  mode == "remove" &&
                  <div
                    onClick={() => {
                      setIsModalOpen(false);
                    }}
                    className="flex flex-1 cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center h-[3.13rem] rounded-[0.625rem]"
                  >
                    Cancel
                  </div>
                }
                <div
                  onClick={() => {
                    if (mode === "edit") {
                      handleChangeLink(selectedProofIndex, linkInput);
                    } else {
                      handleRemoveLink(selectedProofIndex);
                    }
                    setIsModalOpen(false);
                  }}
                  className={`flex flex-1 cursor-pointer font-medium bg-black text-white border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]`}
                >
                  {mode === "edit" ? "Save" : "Remove"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {dashData?.length > 0 ? (
        <div className="flex-1 flex relative flex-col px-[3.81rem]">
          <AppButton
            onClick={() => {
              router.push("/u/add-social-proof");
            }}
            widthClass="w-[11.81rem] mt-[1.88rem] ml-auto"
            text="Add social proof"
          />
          <div className="grid grid-cols-4 gap-[1.06rem] mt-[1.88rem] mx-[0.57rem]">
            {dashData.map((proof, index) => (
              <SocialProofCard
                onEditPress={() => {
                  setSelectedProofIndex(index);
                  setLinkInput(proof.source); // Set the current link for editing
                  setMode("edit");
                  setIsModalOpen(true);
                }}
                onRemovePress={() => {
                  setSelectedProofIndex(index);
                  setMode("remove");
                  setIsModalOpen(true);
                }}
                key={index}
                proof={proof}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="flex-1 flex relative flex-col items-center justify-center">
          <img src="/store.svg" className="w-[2.5rem] h-[2.5rem]" />
          <div className="mt-[0.75rem] font-medium">You don't have a store set up</div>
          <div className="mt-[0.56rem] text-[0.875rem] text-subtext w-[13.5rem] text-center">
            You have to setup a store to get started
          </div>
          <AppButton
            onClick={() => {
              router.push("/p-catalog/setup");
            }}
            widthClass="w-[11.81rem] mt-[1.19rem]"
            text="Set up store"
          />
        </div>
      )}
    </>
  );
}
