"use client";

import Card<PERSON><PERSON>plate from "@/components/CardTemplate";
import GoBackButton from "@/components/GoBackButton";
import { useNavigationStore } from "@/store/navigationStore";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useMediaQuery } from "usehooks-ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import client from "@/api/client";
import { getToken, getUserId } from "@/app/tokenStorage";
import { useAuthStore } from "@/store/authStore";


export default function SelectCardTemplate() {
    const router = useRouter();
    const [selectedCard, setSelectedCard] = useState<number>(0);
    const { user, setUser } = useAuthStore();

    const { data: cardTemplates, isLoading, error } = useQuery({
        queryKey: ["contacts"],
        queryFn: async () => {
            const response = await client.get(`/card_creation/templates`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`
                }
            });
            // Assuming the API returns the templates in response.data.data
            return response.data.data.map((item: Record<string, any>) => {
                return { ...item, config: JSON.parse(item.config) }
            })
        },
    });

    const getUserProfile = useMutation({
        mutationFn: async () => {
            return client.post("/profile/portal", { userId: parseInt(getUserId() as string) });
        },
        onSuccess: async (response) => {
            setUser(response.data?.data)
            console.log("Profile retrieval successful:", response.data);

        },
        onError: (error: any) => {
            console.error("Profile retrieval failed:", error);
            router.replace("/login")
        },
    });

    useEffect(() => {
        if (!user) {
            getUserProfile.mutate();
        }
    }, [user])

    if (isLoading) {
        return <div className="mt-[1.19rem] px-[1rem] sm:px-[4.38rem]">Loading card templates...</div>
    }

    return (
        <div className="flex flex-col mt-[1.19rem] px-[1rem] sm:px-[4.38rem]">
            <div><GoBackButton /></div>

            <div className="xl:px-[6.3rem] mt-[0.69rem]">
                <div className="text-[1.375rem] font-medium mb-[0.5rem]">
                    Select card template
                </div>
                <div className="w-full max-w-[36.69rem] text-subtext mb-[1.64rem]">
                    Choose a template to showcase your personal brand, share your contact
                    details, and present yourself professionally.
                </div>
                <div className="flex w-full -xl:overflow-x-auto gap-[0.25rem] sm:gap-[1.13rem]">
                    {cardTemplates ?
                        cardTemplates.map((template: any, index: number) => (
                            <div
                                key={template.id}
                                onClick={() => { 
                                    setSelectedCard(template.id);
                                 }}
                                className="cursor-pointer"
                            >
                                <CardTemplate {...template.config} selected={selectedCard === template.id} />
                            </div>
                        )) :
                        <div>Loading card templates...</div>
                    }
                </div>

                <div
                    onClick={() => {
                        router.push(`/create-card?${new URLSearchParams({ cardType: selectedCard.toString() }).toString()}`);
                    }}
                    className="flex mt-[1.88rem] cursor-pointer items-center justify-center w-full max-w-[26.88rem] h-[3.13rem] rounded-[0.625rem] bg-black text-white font-medium"
                >
                    Next
                </div>
            </div>
        </div>
    );
}
