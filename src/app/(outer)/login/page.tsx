"use client";

import { useMutation } from "@tanstack/react-query";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import client from "@/api/client";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { decodeToken, getUserId, setToken, setUserId } from "@/app/tokenStorage";
import { useNotification } from "@/contexts/NotificationProvider";
import { useAuthStore } from "@/store/authStore";

const validationSchema = Yup.object({
  emailAddress: Yup.string().email("Invalid email address").required("Email is required"),
  password: Yup.string().required("Password is required"),
});

export default function Home() {
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();
  const { setUser } = useAuthStore();
  const { showNotification } = useNotification();

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      setErrorMessage("");
      return client.post("/login", values);
    },
    onSuccess: async (response) => {
      console.log("Login successful:", response.data);
      console.log("User ID:", response?.data?.data?.id);
      // Retrieve token from the response headers.
      const token =
        response.headers["authorization"]
      if (token) {
        console.log(token)
        console.log(decodeToken(token))
        setToken(token);
        setUserId(response?.data?.data?.id)
        const { data } = await client.post("/profile/portal", { userId: response?.data?.data?.id })
        console.log(data)
        setUser({ ...data.data, signUpComplete: response?.data?.data?.signUpComplete })
        // Navigate to the dashboard
        showNotification({ renderObj: { midSection: "Login successful" }, type: "success" })
        if (response?.data?.data?.signUpComplete){
          router.push("/dashboard");
        } else {
          router.push("/select-card-template");
        }
      }
    },
    onError: (error: any) => {
      console.error("Login failed:", error);
      showNotification({ renderObj: { midSection: "Login failed" }, type: "failure" })
      setErrorMessage(
        error?.response?.data?.message || error?.message || "Login failed. Please try again."
      );
    },
  });


  return (
    <div className="flex flex-col items-center sm:mt-[5.13rem]">
      <div className="w-full max-w-[32.31rem] px-[2.69rem] pt-[1.69rem] pb-[2.25rem] bg-white rounded-[0.625rem] sm:shadow-lg">
        <div className="text-[1.375rem] font-medium mb-[1.94rem]">Log into your account</div>

        {errorMessage && (
          <div className="border border-red-500 bg-red-100 text-red-500 text-center px-[1rem] py-[0.5rem] rounded-lg font-semibold mb-4">
            {errorMessage}
          </div>
        )}

        <Formik
          initialValues={{ emailAddress: "", password: "" }}
          validationSchema={validationSchema}
          onSubmit={(values, { setSubmitting }) => {
            mutation.mutate(values, {
              onSettled: () => setSubmitting(false), // Ensures Formik stops submitting
            });
          }}
        >
          {({ isSubmitting }) => (
            <Form>
              <div className="flex flex-col gap-[1.12rem]">
                <FormikAppTextInput name="emailAddress" label="Email" placeholder="Enter email address" type="email" />
                <FormikAppTextInput name="password" label="Password" placeholder="Enter password" type="password" />
              </div>

              <div onClick={() => router.push('/forgot-password')} className="flex cursor-pointer hover:font-semibold justify-end text-green font-medium text-[0.9375rem] mt-[1rem]">
                Forgot password?
              </div>

              <FormikAppSubmit
                text="Login"
                widthClass="w-full mt-[0.62rem]"
                disabled={isSubmitting || mutation.isPending}
              />
            </Form>
          )}
        </Formik>
      </div>

      <div className="mt-[1.68rem] text-[0.9375rem] font-medium gap-1 flex">
        <div className="text-subtext">{"Don't have an account?"}</div>
        <div onClick={() => router.push('/sign-up')} className="cursor-pointer hover:underline">
          {"Sign up"}
        </div>
      </div>
    </div>
  );
}
