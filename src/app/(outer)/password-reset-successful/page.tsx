"use client";

import { useRouter } from "next/navigation";

export default function PasswordResetSuccessful() {

    const router = useRouter()

    return (
        <div className="flex flex-col text-center items-center mt-[16.97rem]">
            <img src={"/checked.svg"} />
            <div className="text-[1.25rem] font-medium mt-[0.66rem]">
                Password reset successful!
            </div>
            <div className="text-[0.9375rem] text-subtext mt-[0.43rem]">
                Your password has been successfully reset!
            </div>
            <div onClick={() => { router.push("/login") }} className="text-[0.9375rem] cursor-pointer border-b border-green text-green mt-[0.81rem]">
                {"Go to login"}
            </div>
        </div>
    );
}
