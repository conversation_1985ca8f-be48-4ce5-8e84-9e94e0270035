"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import AppTextInput from "@/components/AppTextInput";
import { useNavigationStore } from "@/store/navigationStore";

export default function OrderSuccessful() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);
  const { clearData } = useNavigationStore();


  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev > 0) return prev - 1;
        return 0; // Don't go below 0
      });
    }, 1000);

    const timeout = setTimeout(() => {
      router.push("/dashboard");
    }, 5000);

    clearData();

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [router]);

  return (
    <div className="flex flex-col text-center items-center mt-[16.97rem]">
      <img src={"/checked.svg"} className="w-[2.69rem] h-[2.69rem]" />
      <div className="text-[1.25rem] font-medium mt-[0.66rem]">
        Order successfully placed!
      </div>
      <div className="text-[0.9375rem] w-full max-w-[27.06rem] text-subtext mt-[0.44rem]">
        {/* Your order for 4 Beyondcards has been placed. */}
        Your order has been placed.
        <br />
        Redirecting to your dashboard in <span className="font-semibold">{countdown}</span>...
      </div>
    </div>
  );
}
