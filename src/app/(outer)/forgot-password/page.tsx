"use client";

import { useRouter } from "next/navigation";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { useState } from "react";
import { useNotification } from "@/contexts/NotificationProvider";
import { useNavigationStore } from "@/store/navigationStore";

const validationSchema = Yup.object({
    emailAddress: Yup.string().email("Invalid email address").required("Email is required"),
});

export default function ForgotPassword() {
    const [errorMessage, setErrorMessage] = useState("");
    const { showNotification } = useNotification();
    const { clearData, setData } = useNavigationStore();
    const router = useRouter();

    const mutation = useMutation({
        mutationFn: async (values: { emailAddress: string }) => {
            return client.post("/forgot_password", values);
        },
        onSuccess: (data, variables) => {
            setData({ emailAddress: variables.emailAddress })
            showNotification({ renderObj: { midSection: "Email sent" }, type: "success" })
            // router.push(`/reset-password?${new URLSearchParams(variables).toString()}`);
            router.push(`/reset-password`);
        },
        onError: (error: any) => {
            clearData();
            showNotification({ renderObj: { midSection: error }, type: "failure" })
            console.error("Error:", error);
        },
    });

    return (
        <div className="flex flex-col items-center sm:mt-[5.13rem]">
            <div className="w-full max-w-[32.31rem] px-[2.69rem] pt-[1.69rem] pb-[2.69rem] bg-white rounded-[0.625rem] sm:shadow-lg">
                <div className="text-[1.375rem] font-medium">Forgot Password</div>
                <div className="text-[0.8125rem] text-subtext mb-[1.32rem]">
                    Enter the email address associated with your account, and we'll send a password reset link.
                </div>

                <Formik
                    initialValues={{ emailAddress: "" }}
                    validationSchema={validationSchema}
                    onSubmit={(values, { setSubmitting }) => {
                        mutation.mutate(values, { onSettled: () => setSubmitting(false) });
                    }}
                >
                    {({ isSubmitting }) => (
                        <Form className="flex flex-col gap-[1.12rem]">
                            <FormikAppTextInput name="emailAddress" label="Email" placeholder="Enter email address" type="email" />

                            <FormikAppSubmit text="Send email" widthClass="w-full mt-[1.56rem]" disabled={isSubmitting || mutation.isPending} />
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
}
