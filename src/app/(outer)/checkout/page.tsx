"use client";

import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";

import GoBackButton from "@/components/GoBackButton";
import BCard from "@/components/BCard";          // still imported (not used here but stays)
import Card<PERSON>emplate from "@/components/CardTemplate"; // likewise (kept for parity)
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import { useAuthStore } from "@/store/authStore";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import { getUserId } from "@/app/tokenStorage";
import { useNavigationStore } from "@/store/navigationStore";
import { useNotification } from "@/contexts/NotificationProvider";

/* ---------- Yup schema ---------- */
const CheckoutSchema = Yup.object({
    fullName: Yup.string().trim().required("Full name is required"),
    emailAddress: Yup.string().trim().email("Invalid email").required("Email is required"),
    phoneNumber: Yup.string()
        .trim()
        .matches(/^\+?\d{7,15}$/, "Enter a valid phone number")
        .required("Phone number is required"),
    country: Yup.string().trim().required("Country is required"),
    streetAddress: Yup.string().trim().required("Street address is required"),
    town: Yup.string().trim().required("Town/City is required"),
    region: Yup.string().trim().required("State/County/Region is required"),
    coordinates: Yup.string()
        .nullable()
        .transform((v) => (v === "" ? null : v))
        .url("Enter a valid URL"),
});


export default function Checkout() {
    const router = useRouter();
    const { user, setUser } = useAuthStore();
    const { data } = useNavigationStore();
    const { showNotification } = useNotification()

    console.log(data)

    // const initializePayment = useMutation({
    //     mutationFn: async (values: any) => {
    //         return client.post("/payment/initialize", {
    //             email: values.emailAddress,
    //             amount: (
    //                 (data?.cardDetails?.quantity ?? 0) *
    //                 ((data?.cardDetails?.price ?? 0))
    //             ).toFixed(2),
    //             callback_url: window.location.origin + "/order-successful",
    //         });
    //     },
    //     onSuccess: async (response) => {
    //         console.log("Payment successful:", response.data);
    //         window.location.href = response.data.data.authorizationUrl;
    //         // router.replace("/order-successful")
    //     },
    //     onError: (error: any) => {
    //         console.error("Checkout failed:", error);
    //     },
    // });

    const checkout = useMutation({
        mutationFn: async (values: any) => {
            return client.post("/checkout", {
                ...values,
                shipping: 0.00,
                tax: 0.00,
                subtotal: parseFloat((
                    (data?.cardDetails?.quantity ?? 0) *
                    ((data?.cardDetails?.price ?? 0))
                ).toFixed(2)),
                total: parseFloat((
                    (data?.cardDetails?.quantity ?? 0) *
                    ((data?.cardDetails?.price ?? 0))
                ).toFixed(2)),
                quantity: data?.cardDetails?.quantity,
                cardId: data?.cardDetails?.id,
                cardTemplateId: data.cardDetails?.cardId
            });
        },
        onSuccess: async (response, variables) => {
            // setUser(response.data?.data)
            console.log("Checkout successful:", response.data);
            showNotification({ renderObj: { midSection: "Initializing payment" }, type: "info" })
            window.location.href = response.data.data.authorizationUrl;
            // router.replace("/order-successful")
        },
        onError: (error: any) => {
            console.error("Checkout failed:", error);
        },
    });

    const getUserProfile = useMutation({
        mutationFn: async () => {
            return client.post("/profile/portal", { userId: parseInt(getUserId() as string) });
        },
        onSuccess: async (response) => {
            setUser(response.data?.data)
            console.log("Profile retrieval successful:", response.data);
        },
        onError: (error: any) => {
            const msg = error?.response?.data?.message || error?.message || "Checkout failed"
            showNotification({ renderObj: { midSection: msg }, type: "failure" })
        },
    });

    useEffect(() => {
        if (!user) {
            getUserProfile.mutate();
        }
    }, [user])


    return (
        <Formik
            initialValues={{
                fullName: `${data?.cardDetails?.firstName} ${data?.cardDetails?.lastName}` || `${user?.firstName} ${user?.lastName}` || "",
                emailAddress: user?.emailAddress || "",
                phoneNumber: user?.phoneNumber || "",
                country: "",
                streetAddress: "",
                town: "",
                region: "",
                coordinates: "",
            }}
            validationSchema={CheckoutSchema}
            onSubmit={(values, { setSubmitting }) => {
                checkout.mutate(values, {
                    onSettled: () => setSubmitting(false), // Ensures Formik stops submitting
                });
            }}
        >
            <Form className="flex flex-col flex-1 mt-[1.19rem] px-[1rem] md:px-[2rem] xl:px-[4.38rem]">
                {/* ---------- header ---------- */}
                <div>
                    <GoBackButton />
                </div>

                {/* ---------- scroll area ---------- */}
                <div className="flex-1 relative overflow-y-auto">
                    <div className="absolute w-full h-full">
                        <div className="text-[1.375rem] sm:px-[2rem] lg:px-[4rem] xl:px-[11.57rem] font-medium mt-[0.69rem] mb-[1.75rem]">
                            Checkout
                        </div>

                        <div className="flex flex-1 -md:flex-col lg:gap-[2rem] xl:gap-[5.56rem] sm:px-[2rem] lg:px-[4rem] xl:px-[11.57rem]">
                            {/* ---------- left column (form fields) ---------- */}
                            <div className="flex-col w-full max-w-[31.06rem] md:flex-1 relative overflow-y-auto hide-scrollbar">
                                <div className="w-full">
                                    {/* section 01 */}
                                    <div className="flex text-[1.125rem] gap-1 font-semibold mb-[1.06rem]">
                                        <span className="text-subtext">01.</span>
                                        <span className="text-headertext">Card Recipient details</span>
                                    </div>

                                    <div className="flex flex-col gap-[0.94rem] mb-[4.13rem]">
                                        <FormikAppTextInput
                                            name="fullName"
                                            label="Full name"
                                            placeholder="Enter your full name"
                                            type="text"
                                        />
                                        <FormikAppTextInput
                                            name="emailAddress"
                                            label="Email"
                                            placeholder="Enter your email"
                                            type="email"
                                        />
                                        <FormikAppTextInput
                                            name="phoneNumber"
                                            label="Phone number"
                                            placeholder="Enter your phone number"
                                            type="text"
                                        />
                                    </div>

                                    {/* section 02 */}
                                    <div className="flex text-[1.125rem] gap-1 font-semibold mb-[1.06rem]">
                                        <span className="text-subtext">02.</span>
                                        <span className="text-headertext">Shipping/Delivery details</span>
                                    </div>

                                    <div className="flex flex-col gap-[0.94rem] mb-[1.75rem] md:mb-[4.13rem]">
                                        <FormikAppTextInput
                                            name="country"
                                            label="Country"
                                            placeholder="Country"
                                            type="text"
                                        />
                                        <FormikAppTextInput
                                            name="streetAddress"
                                            label="Street address"
                                            placeholder="Street Address"
                                            type="text"
                                        />

                                        <div className="flex md:flex-col gap-[0.38rem] md:gap-[0.94rem]">
                                            <FormikAppTextInput
                                                name="town"
                                                label="Town/City"
                                                placeholder="Town/City"
                                                type="text"
                                            />
                                            <FormikAppTextInput
                                                name="region"
                                                label="State/County/Region"
                                                placeholder="State/County/Region"
                                                type="text"
                                            />
                                        </div>

                                        <FormikAppTextInput
                                            name="coordinates"
                                            label="Google Map Coordinates (optional)"
                                            placeholder="Enter google map location URL"
                                            type="text"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* ---------- right column (summary + submit) ---------- */}
                            <div className="w-full md:max-w-[25.56rem]">
                                <div className="bg-grey rounded-[0.625rem] border border-outline pt-[1.13rem] pb-[1.63rem] pl-[1.44rem] pr-[1.31rem]">
                                    <div className="flex items-center justify-between text-placeholder font-medium">
                                        <div>Subtotal</div>
                                        <div>
                                            GH₵
                                            {(
                                                (data?.cardDetails?.quantity ?? 0) *
                                                (data?.cardDetails?.price ?? 0)
                                            ).toFixed(2)}
                                        </div>
                                    </div>
                                    <div className="flex items-center mt-[1.19rem] justify-between text-placeholder font-medium">
                                        <div>Shipping</div>
                                        <div>GH₵0.00</div>
                                    </div>
                                    <div className="flex items-center mt-[1.19rem] justify-between text-placeholder font-medium">
                                        <div>Tax</div>
                                        <div>GH₵0.00</div>
                                    </div>
                                    <div className="flex items-center mt-[2.63rem] justify-between text-headertext font-medium">
                                        <div>Total</div>
                                        <div>
                                            GH₵
                                            {(
                                                (data?.cardDetails?.quantity ?? 0) *
                                                (data?.cardDetails?.price ?? 0)
                                            ).toFixed(2)}
                                        </div>
                                    </div>

                                    {/* submit button */}
                                    <button
                                        type="submit"
                                        className="flex mt-[1.88rem] items-center justify-center w-full md:max-w-[22.81rem] h-[3.13rem] rounded-[0.625rem] bg-black text-white font-medium"
                                    >
                                        Place order
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Form>
        </Formik>
    );
}
