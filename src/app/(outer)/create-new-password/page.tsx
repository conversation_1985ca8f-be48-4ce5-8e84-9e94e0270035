"use client";

import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import AppTextInput from "@/components/AppTextInput";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { useNotification } from "@/contexts/NotificationProvider";
import client from "@/api/client";
import { getBearerToken, getToken } from "@/app/tokenStorage";

const validationSchema = Yup.object({
    newPassword: Yup.string().required("Password is required").min(8, "Must be at least 8 characters"),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword")], "Passwords must match")
        .required("Please confirm your password"),
});

export default function CreateNewPassword() {
    const [errorMessage, setErrorMessage] = useState("");
    const router = useRouter();
    const { showNotification } = useNotification();

    const mutation = useMutation({
        mutationFn: async (values: { newPassword: string }) => {
            setErrorMessage("");
            return client.post("/auth/change_password", values, {
                headers: {
                    Authorization: getBearerToken()
                }
            });
        },
        onSuccess: () => {
            showNotification({
                renderObj: { midSection: "Password successfully updated" },
                type: "success",
            });
            router.push("/login"); // Redirect after success
        },
        onError: (error: any) => {
            console.error("Error resetting password:", error);
            setErrorMessage(
                error?.response?.data?.message || error?.message || "Something went wrong. Please try again."
            );
            showNotification({
                renderObj: { midSection: "Password reset failed" },
                type: "failure",
            });
        },
    });

    return (
        <div className="flex flex-col items-center sm:mt-[5.13rem]">
            <div className="w-full max-w-[32.31rem] px-[2.69rem] py-[1.69rem] bg-white rounded-[0.625rem] sm:shadow-lg">
                <div className="text-[1.375rem] font-medium mb-[1.94rem]">Create new password</div>

                {errorMessage && (
                    <div className="border border-red-500 bg-red-100 text-red-500 text-center px-4 py-2 rounded text-sm font-medium mb-4">
                        {errorMessage}
                    </div>
                )}

                <Formik
                    initialValues={{ newPassword: "", confirmPassword: "" }}
                    validationSchema={validationSchema}
                    onSubmit={(values, { setSubmitting }) => {
                        mutation.mutate(values, {
                            onSettled: () => setSubmitting(false),
                        });
                    }}
                >
                    {({ isSubmitting }) => (
                        <Form>
                            <div className="flex flex-col gap-[1.12rem]">
                                <FormikAppTextInput
                                    name="newPassword"
                                    label="Create new password"
                                    placeholder="Create password"
                                    type="password"
                                />
                                <FormikAppTextInput
                                    name="confirmPassword"
                                    label="Confirm new password"
                                    placeholder="Confirm password"
                                    type="password"
                                />
                            </div>

                            <FormikAppSubmit
                                text="Reset password"
                                widthClass="w-full mt-[1.87rem]"
                                disabled={isSubmitting || mutation.isPending}
                            />
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
}
