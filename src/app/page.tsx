"use client";

import AppTextInput from "@/components/AppTextInput";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function Home() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const router = useRouter()

  return (
    <div className="flex flex-col items-center mt-[5.13rem]">
      <div className="w-[32.31rem] px-[2.69rem] pt-[1.69rem] pb-[2.25rem] bg-white rounded-[0.625rem] shadow-lg">
        <div onClick={() => router.push('/login')} className="flex cursor-pointer items-center justify-center h-[3.13rem] rounded-[0.625rem] bg-black text-white font-medium mt-[0.62rem]">
          Login Page
        </div>
      </div>
    </div>
  );
}
