import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>, <PERSON>_<PERSON>_Display } from "next/font/google";


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <div className="lg:hidden flex items-center bg-white h-[5.56rem] px-[1rem] sm:px-[4.38rem] border-b border-outline">
        <img src={"/Logo.svg"} className="h-[1.79rem] w-[7.5rem]" />
      </div>
      {children}
    </>
  );
}
