"use client";

import { useMutation } from "@tanstack/react-query";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import { useState } from "react";
import CheckItem from "@/components/CheckItem";
import client from "@/api/client";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { useNavigationStore } from "@/store/navigationStore";
import { useAuthStore } from "@/store/authStore";

const validationSchema = Yup.object({
  firstName: Yup.string().required("First name is required"),
  lastName: Yup.string().required("Last name is required"),
  emailAddress: Yup.string().email("Invalid email").required("Email is required"),
  password: Yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
});

export default function SignUp() {
  const [errorMessage, setErrorMessage] = useState("");
  const [termsCheck, setTermsCheck] = useState(false);
  const {setUser} = useAuthStore()

  const router = useRouter();

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      setErrorMessage("");
      return client.post("/signup", values);
    },
    onSuccess: (response) => {
      console.log("Signup successful:", response.data); // Print the successful response
      const rd = response?.data?.data
      setUser(rd)
      router.push(`/verify-email/${rd.id}`);
    },
    onError: (error: any) => {
      console.error("Signup failed:", error); // Log the full error
      console.error("Error response:", error?.response?.data); // Log the response data
      setErrorMessage(error?.response?.data?.message || error?.message || "Signup failed. Please try again.");
    },
  });

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="flex gap-[7.69rem]">
        <div className="-lg:hidden pl-[1.13rem] pt-[10.18rem]">
          <img src={"/Logo.svg"} className="h-[2.4rem] w-[10.07rem]" />
          <div className="flex flex-col gap-[1.56rem] mt-[3.39rem]">
            <CheckItem
              iconSrc="/check-circle-orange.svg"
              title="Elevate your presence"
              description="Showcase your achievements, share testimonials, and present social proofs."
            />
            <CheckItem
              iconSrc="/check-circle-orange.svg"
              title="Stay relevant and connected"
              description="Keep your information up-to-date and easily share it electronically, fostering meaningful connections."
            />
          </div>
        </div>

        <div className="flex flex-col items-center">
          <div className="w-full max-w-[32.31rem] px-[2.69rem] pt-[1.69rem] pb-[2.25rem] bg-white rounded-[0.625rem] sm:shadow-xl">
            <div className="text-[1.375rem] font-medium mb-[1.94rem]">Create your account</div>

            {errorMessage && <div className="border border-red-500 bg-red-100 text-red-500 text-center px-[1rem] py-[0.5rem] rounded-lg font-semibold mb-4">{errorMessage}</div>}

            <Formik
              initialValues={{ firstName: "", lastName: "", emailAddress: "", password: "" }}
              validationSchema={validationSchema}
              onSubmit={(values, { setSubmitting }) => {
                mutation.mutate(values, {
                  onSettled: () => setSubmitting(false), // Ensures Formik stops submitting
                });
              }}
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="flex flex-col gap-[1.13rem]">
                    <FormikAppTextInput label="First name" name="firstName" placeholder="Enter first name" />
                    <FormikAppTextInput label="Last name" name="lastName" placeholder="Enter last name" />
                    <FormikAppTextInput label="Email" name="emailAddress" type="email" placeholder="Enter email address" />
                    <FormikAppTextInput label="Create password" name="password" type="password" placeholder="Create password" />
                  </div>

                  <div className="text-[#333333] mt-[1.88rem] mb-[0.5rem] text-base flex gap-[0.3rem]">
                    <div className="py-0.5">
                      <input
                        type="checkbox"
                        onChange={() => setTermsCheck((prev) => !prev)}
                        checked={termsCheck}
                        className="w-[0.85rem] cursor-pointer h-[0.85rem] accent-[#111111]"
                      />
                    </div>
                    <div className="text-headertext text-[0.9125rem]">
                      I have reviewed and agreed to the{" "}
                      <span className="cursor-pointer underline font-semibold">Terms of Use</span> and{" "}
                      <span className="cursor-pointer underline font-semibold">Privacy Notice</span>
                    </div>
                  </div>

                  <FormikAppSubmit
                    text="Sign up"
                    widthClass="w-full"
                    disabled={!termsCheck || isSubmitting || mutation.isPending}
                  />
                </Form>
              )}
            </Formik>
          </div>

          <div className="sm:mt-[1.63rem] text-[0.9375rem] font-medium gap-1 flex">
            <div className="text-subtext">{"Already have an account?"}</div>
            <div onClick={() => router.push("/login")} className="cursor-pointer hover:underline">
              {"Log in"}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
