@tailwind base;
@tailwind components;
@tailwind utilities;

/* For font look matching with Figma */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Hide scrollbar classes */
/* For Chrome, Safari, and Edge */
.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* For Firefox */
.hide-scrollbar {
    scrollbar-width: none;
}

/* For Internet Explorer */
.hide-scrollbar {
    -ms-overflow-style: none;
}

/* Thin scrollbar classes */
/* For Chrome, Safari, and Edge */
.thin-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5); /* Change color as needed */
    border-radius: 10px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.7); /* Darker on hover */
}

/* For Firefox */
.thin-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.5) transparent; /* Thumb color and track color */
}

.thin-scrollbar:hover {
    scrollbar-color: rgba(0, 0, 0, 0.7) transparent; /* Darker thumb on hover */
}
