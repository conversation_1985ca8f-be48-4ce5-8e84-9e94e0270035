import { NextResponse } from "next/server";
import axios from "axios";

export async function POST(req: Request) {
  try {
    const { url } = await req.json();
    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Ensure it's a valid maps.app.goo.gl URL
    if (!url.includes("maps.app.goo.gl")) {
      return NextResponse.json({ error: "Not a valid Google Maps short URL" }, { status: 400 });
    }

    // Try resolving the redirect (no following of redirects automatically)
    const response = await axios.get(url, {
      maxRedirects: 0,
      validateStatus: (status) => status >= 300 && status < 400,
      headers: { "User-Agent": "Mozilla/5.0" },
    });

    const location = response.headers.location;

    if (!location) {
      return NextResponse.json({ error: "Could not resolve redirect" }, { status: 500 });
    }

    return NextResponse.json({ expandedUrl: location });
  } catch (error) {
    console.error("Error expanding maps URL:", error);
    return NextResponse.json({ error: "Failed to expand URL" }, { status: 500 });
  }
}
