import { NextResponse } from "next/server";
import axios from "axios";
import * as cheerio from "cheerio";

export async function POST(req: Request) {
  try {
    const { url } = await req.json();
    if (!url) return NextResponse.json({ error: "URL is required" }, { status: 400 });

    const { data } = await axios.get(url.startsWith("http") ? url : `https://${url}`, {
      headers: { "User-Agent": "Mozilla/5.0" }, // Helps prevent some blocks
    });

    const $ = cheerio.load(data);
    const title = $("title").text() || $("meta[property='og:title']").attr("content") || null;
    const ogImage = $("meta[property='og:image']").attr("content") || null;

    return NextResponse.json({ title, ogImage });
  } catch (error) {
    console.error("Error fetching OG image:", error);
    return NextResponse.json({ error: "Failed to fetch OG image" }, { status: 500 });
  }
}
