"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import ServiceListItem from "@/components/ServiceListItem";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { BsWhatsapp } from "react-icons/bs";
import { MdEmail } from "react-icons/md";
import { TfiEmail } from "react-icons/tfi";

const serviceListData = [
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Product Development Strategy",
        description:
            "Comprehensive planning and execution strategies for product lifecycle management.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Agile Coaching",
        description:
            "Training teams to adopt agile practices and improve product delivery timelines.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Market Research & Analysis",
        description:
            "In-depth market insights to align product features with customer needs.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Product Launch Management",
        description:
            "End-to-end coordination of new product launches for maximum impact.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "KPI and Metrics Optimization",
        description:
            "Setting and tracking measurable outcomes for product success.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Stakeholder Management Consulting",
        description:
            "Ensuring clear communication and alignment between teams and stakeholders.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Product Lifecycle Audit",
        description:
            "Evaluating existing products to identify areas for optimization or enhancement.",
    },
    {
        imageSrc: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        altText: "Random",
        title: "Team Training & Workshops",
        description:
            "Conducting workshops on modern product management techniques for businesses and startups.",
    },
];


export default function ServiceCatalog() {
    const [detailModalOpen, setDetailModalOpen] = useState(false)
    const [emailEnquiryModalOpen, setEmailEnquiryModalOpen] = useState(false);
    const [whatsappEnquiryModalOpen, setWhatsappEnquiryModalOpen] = useState(false);
    const [selectedService, setSelectedService] = useState<any>(null)

    const router = useRouter()

    return (
        <div className="flex relative flex-col w-full h-screen items-center">
            <Modal isOpen={detailModalOpen}>
                <div className="flex flex-col justify-start pt-10 px-[0.5rem] bg-altbg2 items-center w-screen h-screen overflow-y-auto hide-scrollbar bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => { setDetailModalOpen(false); }}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Service Detail
                        </div>
                        <div className="flex w-full flex-col flex-1 px-[0.5rem] mt-[0.75rem] pb-[1rem] overflow-y-auto hide-scrollbar">
                            <div className="h-[9.13rem] w-full mb-[0.75rem]">
                                <img
                                    src={selectedService?.imageSrc}
                                    alt={selectedService?.altText}
                                    className="h-full w-full object-cover rounded-[0.75rem]"
                                />
                            </div>

                            <div className="bg-white shadow-xl pt-[0.57rem] pb-[1.31rem] px-[0.94rem] rounded-[0.625rem] w-full">
                                <div className="text-headertext font-medium">{selectedService?.title}</div>
                                <div className="text-[0.8125rem] text-subtext mt-[0.25rem]">
                                    {selectedService?.description}
                                </div>
                            </div>

                            <div className="flex flex-col gap-[0.75rem] mt-5">
                                <div onClick={() => { setEmailEnquiryModalOpen(true); setDetailModalOpen(false) }} className="flex justify-center items-center text-white bg-black font-medium items-center rounded-[0.625rem] border border-black h-[3.13rem]">
                                    <div>Make enquiries</div> <TfiEmail className="text-lg ml-2" />
                                </div>
                                {/* <div onClick={() => { setWhatsappEnquiryModalOpen(true); setDetailModalOpen(false) }} className="flex text-white bg-green justify-center font-medium items-center rounded-[0.625rem] border border-green h-[3.13rem]">
                                    Via WhatsApp <BsWhatsapp className="text-lg ml-2" />
                                </div> */}
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            <Modal isOpen={emailEnquiryModalOpen}>
                <div className="flex flex-col justify-start pt-10 px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => { setEmailEnquiryModalOpen(false); setDetailModalOpen(false); }}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Leave enquiry
                        </div>
                        <div className="flex flex-col gap-[0.43rem] mt-[1.25rem]">
                            <AppTextInput label="Your name" placeholder="Enter name" />
                            <AppTextInput label="Email" placeholder="Enter email" />
                            <AppTextArea label="Enquiry" placeholder="Ask a question" />
                            <div className='text-[#333333] mt-[1rem] mb-[0.5rem] text-base flex items-center gap-[0.3rem]'>
                                <div className=''>
                                    <input type="checkbox" onChange={() => { }} className='w-[0.9rem] cursor-pointer h-[0.9rem] accent-[#111111]' />
                                </div>
                                <div className="text-headertext text-[0.9125rem] font-semibold flex items-center">Ask on Whatsapp <BsWhatsapp className="text-[1rem] text-headertext ml-2" /></div>
                            </div>
                        </div>
                        <div className="flex mt-[1.6rem] justify-center items-center gap-[0.62rem]">
                            <div onClick={() => { setEmailEnquiryModalOpen(false); setDetailModalOpen(true); }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-grey items-center bg-grey text-black justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Go back
                            </div>
                            <div onClick={() => { }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Submit
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            <Modal isOpen={whatsappEnquiryModalOpen}>
                <div className="flex flex-col justify-start pt-10 px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => { setWhatsappEnquiryModalOpen(false); setDetailModalOpen(false); }}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Leave enquiry
                        </div>
                        <div className="flex flex-col gap-[0.43rem] mt-[1.25rem]">
                            <AppTextInput label="Your name" placeholder="Enter name" />
                            <AppTextInput label="Phone" placeholder="Enter phone" />
                            <AppTextArea label="Initial question" placeholder="Ask a question" />
                        </div>
                        <div className="flex mt-[1.6rem] justify-center items-center gap-[0.62rem]">
                            <div onClick={() => { setWhatsappEnquiryModalOpen(false); setDetailModalOpen(true); }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-grey items-center bg-grey text-black justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Go back
                            </div>
                            <div onClick={() => { }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Proceed to Whatsapp
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            <div className="flex flex-col bg-white w-full max-w-[52.81rem] mx-auto shadow-lg border-b-[0.3rem] border-gray-300 pt-[1.94rem] pb-[0.76rem] px-[1.06rem]">
                <div onClick={() => { router.back() }} className="flex justify-center cursor-pointer bg-grey items-center w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem]">
                    <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" />
                    <div className="text-placeholder text-[0.875rem]">Go back</div>
                </div>

                <div className="mt-[1.56rem] text-headertext px-[0.44rem]">
                    Service catalog
                </div>
            </div>

            <div className="flex w-full flex-col bg-altbg2 max-w-[52.81rem] mx-auto shadow-lg flex-1 px-[1rem] mt-[0.75rem] gap-[0.88rem] pb-[3.38rem] overflow-y-auto hide-scrollbar">
                {serviceListData.map((service, index) => (
                    <ServiceListItem
                        key={index}
                        onClick={() => { setSelectedService(service); setDetailModalOpen(true) }}
                        imageSrc={service.imageSrc}
                        altText={service.altText}
                        title={service.title}
                        description={service.description}
                    />
                ))}
            </div>

        </div>
    );
}
