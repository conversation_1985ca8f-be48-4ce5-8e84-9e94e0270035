"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import ServiceListItem from "@/components/ServiceListItem";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function ServiceDetail() {
    const [isOpen, setIsOpen] = useState(false);
    const router = useRouter()

    return (
        <div className="flex relative flex-col w-full h-screen items-center">
            <Modal isOpen={isOpen}>
                <div className="flex flex-col justify-center px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => setIsOpen(false)}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Leave enquiry
                        </div>
                        <div className="flex flex-col gap-[0.43rem] mt-[1.25rem]">
                            <AppTextInput label="Your name" placeholder="Enter name" />
                            <AppTextInput label="Email" placeholder="Enter email" />
                            <AppTextInput label="Phone" placeholder="Enter phone" />
                            <AppTextArea label="Initial question" placeholder="Ask a question" />
                        </div>
                        <div
                            onClick={() => setIsOpen(false)}
                            className="h-[3.13rem] mt-[1.6rem] rounded-[0.625rem] flex cursor-pointer font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center"
                        >
                            Proceed to WhatsApp
                        </div>
                    </div>
                </div>
            </Modal>

            <div className="flex flex-col bg-white w-full  pt-[1.94rem] pb-[0.76rem] px-[1.06rem]">
                <div onClick={() => { router.back() }} className="flex justify-center bg-grey items-center w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem]">
                    <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" />
                    <div className="text-placeholder text-[0.875rem]">Go back</div>
                </div>

                <div className="mt-[1.56rem] text-headertext px-[0.44rem]">
                    Service catalog
                </div>
            </div>

            <div className="flex w-full flex-col bg-altbg2 flex-1 px-[1rem] mt-[0.75rem] pb-[3.38rem] overflow-y-auto hide-scrollbar">
                <div className="h-[9.13rem] w-full mb-[0.75rem]">
                    <img
                        src="https://picsum.photos/800/600"
                        alt="Random"
                        className="h-full w-full object-cover rounded-[0.75rem]"
                    />
                </div>

                <div className="bg-white shadow-xl pt-[0.57rem] pb-[1.31rem] px-[0.94rem] rounded-[0.625rem] w-full">
                    <div className="text-headertext font-medium">Service name</div>
                    <div className="text-[0.8125rem] text-subtext mt-[0.25rem]">
                        Insurance is a financial product designed to provide protection against specific risks. By paying a premium, individuals or businesses can transfer the financial consequences of potential losses to an insurance company. In exchange, the insurer agrees to compensate the policyholder for covered losses, such as property damage, medical expenses, liability claims, or loss of income. Insurance services offer peace of mind by helping individuals and businesses mitigate the financial impact of unforeseen events, enabling them to recover and rebuild after setbacks.
                    </div>
                </div>

                <div className="mt-[2.25rem] text-center font-medium text-headertext mb-[1.31rem]">Make enquiries</div>

                <div className="flex flex-col gap-[0.75rem]">
                    <div onClick={() => { setIsOpen(true) }} className="flex justify-center font-medium items-center rounded-[0.625rem] border border-black h-[3.13rem]">
                        Via Email
                    </div>
                    <div onClick={() => { setIsOpen(true) }} className="flex justify-center font-medium items-center rounded-[0.625rem] border border-black h-[3.13rem]">
                        Via WhatsApp
                    </div>
                </div>
            </div>
        </div>
    );
}
