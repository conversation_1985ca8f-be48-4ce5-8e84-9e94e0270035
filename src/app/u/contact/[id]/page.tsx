"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { HiLocationMarker } from 'react-icons/hi';
import colors from "../../../../../themePalette";
import ContactNav from "@/components/ContactNav";
import useGeolocation from "@/hooks/useGeolocation";
import { useMutation, useQuery } from "@tanstack/react-query";
import client from "@/api/client";
import { getBearerToken, getToken, getUserId, setContactId } from "@/app/tokenStorage";
import { BsGoogle, BsInstagram, BsLinkedin, BsMeta, BsTiktok, BsTwitterX, BsWhatsapp, BsYoutube } from "react-icons/bs";
import { FaFacebook } from "react-icons/fa";
import { useNavigationStore } from "@/store/navigationStore";
import { MdAccountCircle } from "react-icons/md";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppTextArea from "@/components/formik/FormikAppTextArea";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { useNotification } from "@/contexts/NotificationProvider";
import { Address, formatAddress, generateVCF, getAdrDetailsFromSharedMapsUrl, getEmbedUrlFromGoogleMapsUrl } from "@/utils/helpers";
import { expandMapsUrl } from "@/utils/expandMapsUrl";

// Validation schema
const exchangeSchema = Yup.object().shape({
    name: Yup.string().required("Required"),
    emailAddress: Yup.string().email("Invalid email").required("Required"),
    phoneNumber: Yup.string().required("Required"),
    jobTitle: Yup.string(),
    company: Yup.string(),
    introductoryMessage: Yup.string().required("Required"),
});

export default function Home() {
    const [isOpen, setIsOpen] = useState(false);
    const [profile, setProfile] = useState(false);
    const router = useRouter();
    const { id } = useParams();
    const { setData } = useNavigationStore();
    const { location, error: geo_error } = useGeolocation();
    const { showNotification } = useNotification()
    const [loadingMap, setLoadingMap] = useState(false)
    const [mapEmbedUrl, setMapEmbedUrl] = useState("")
    const [address, setAddress] = useState<Address>()
    const [fullyLoaded, setFullyLoaded] = useState(false)



    const icons = {
        "twitter": <BsTwitterX />,
        "linkedin": <BsLinkedin />,
        "instagram": <BsInstagram />,
        "facebook": <FaFacebook />,
        "tiktok": <BsTiktok />,
        "whatsapp": <BsWhatsapp />,
        "youtube": <BsYoutube />,
        "google": <BsGoogle />
    }

    const exchangeContact = useMutation({
        mutationFn: async (values: any) => {
            const body = { ...values, usersId: parseInt(id as string) }
            console.log(body)
            return client.post("/contacts/guest", body);
        },
        onSuccess: (response) => {
            console.log("Contact exchange successful:", response.data); // Print the successful response
            showNotification({ renderObj: { midSection: "Contact exchange successful" }, type: "success" })
        },
        onError: (error: any) => {
            console.error("Contact exchange failed:", error); // Log the full error
            console.error("Error response:", error?.response?.data); // Log the response data
            showNotification({ renderObj: { midSection: error?.response?.data?.message || error?.message || "Contact exchange failed" }, type: "failure" })
        },
    });

    const { data: userProfile, isLoading, error } = useQuery({
        queryKey: ["user_profile"],
        queryFn: async () => {
            setContactId(id as string)
            const response = await client.post(`/profile/view`, {
                userId: parseInt(id as string), location: JSON.stringify(location)
            });

            setData({ contactData: response.data.data })

            console.log("CONTACT DATA: ", { contactData: response.data.data })

            // alert(JSON.stringify(response.data))
            // Assuming the API returns the templates in response.data.data
            return response.data.data
        },
    });

    useEffect(() => {
        const loadMapData = async () => {
            if (!userProfile?.location) {
                setFullyLoaded(true);
                return;
            }

            setLoadingMap(true);
            try {
                // Process map URL
                if (userProfile?.location) {
                    try {
                        const expandedUrl = await expandMapsUrl(userProfile?.location);
                        if (expandedUrl) {
                            console.log("Expanded Url: ", expandedUrl)
                            const embedUrl = getEmbedUrlFromGoogleMapsUrl(expandedUrl)
                            console.log("Embed Url: ", embedUrl)
                            setMapEmbedUrl(embedUrl)
                        }
                    } catch (error) {
                        console.error(error)
                    }

                    // Get address details
                    const address_ = await getAdrDetailsFromSharedMapsUrl(userProfile?.location)
                    setAddress(address_ as Address)
                    console.log("Address:", address_)
                }
            } catch (error) {
                console.error("Error loading map data:", error);
            } finally {
                setLoadingMap(false);
                setFullyLoaded(true);
            }
        };

        if (userProfile) {
            loadMapData();
        }
    }, [userProfile]);

    // Show loading state until both profile and map are loaded
    if (isLoading || !fullyLoaded) {
        return (
            <div className="w-screen h-screen px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[10rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        )
    }

    return (
        <div className="flex relative flex-col w-full max-w-[52.81rem] mx-auto bg-altbg items-center">
            <Modal isOpen={isOpen}>
                <div className="flex flex-col justify-center relative px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col absolute -md:bottom-10 w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => setIsOpen(false)}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold text-center">
                            Exchange contact
                        </div>
                        <Formik
                            initialValues={{
                                name: "",
                                emailAddress: "",
                                phoneNumber: "",
                                jobTitle: "",
                                company: "",
                                introductoryMessage: "",
                            }}
                            validationSchema={exchangeSchema}
                            onSubmit={(values, { setSubmitting, resetForm }) => {
                                const { name, ...others } = values
                                const names = name.split(" ").map((n) => n.trim())
                                exchangeContact.mutate(
                                    {
                                        ...others,
                                        firstName: names[0],
                                        lastName: names[1]
                                        // tag: [],
                                        // guestImage: "",
                                    },
                                    {
                                        onSettled: () => {
                                            setSubmitting(false);
                                            resetForm();
                                            setIsOpen(false);
                                        },
                                    }
                                );
                            }}
                        >
                            {({ isSubmitting }) => (
                                <Form>
                                    <div className="flex flex-col gap-[0.43rem] mt-[1.06rem]">
                                        <FormikAppTextInput label="Your name" name="name" placeholder="Firstname Lastname" />
                                        <FormikAppTextInput label="Email" name="emailAddress" placeholder="Enter email" />
                                        <FormikAppTextInput label="Phone" name="phoneNumber" placeholder="Enter phone" />
                                        <FormikAppTextInput label="Job title" labelExtra="Optional" name="jobTitle" placeholder="Enter job title" />
                                        <FormikAppTextInput label="Company" labelExtra="Optional" name="company" placeholder="Enter company" />
                                        <FormikAppTextArea rows={3} label="Leave message" labelExtra="Optional" name="introductoryMessage" placeholder="Enter message" />
                                    </div>
                                    <div className="mt-[1.6rem]">
                                        <FormikAppSubmit
                                            text="Exchange contact"
                                            widthClass="w-full"
                                            disabled={isSubmitting || exchangeContact.isPending}
                                        />
                                    </div>
                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>
            </Modal>

            <div
                className="h-[14.38rem] w-full bg-cover"
                style={{ backgroundImage: userProfile?.coverPhoto ? `url('${userProfile?.coverPhoto}')` : "url('/contact-bg.svg')" }}
            ></div>

            <div className="flex max-w-[52.81rem] mx-auto flex-col w-full absolute items-center w-screen h-screen">
                {
                    userProfile?.profileImage ?
                        <img
                            className="absolute z-[2] border-[0.15rem] border-white rounded-full w-[6rem] h-[6rem] mt-[4.3rem] object-cover"
                            src={userProfile.profileImage}
                        />
                        :
                        <MdAccountCircle size={"6rem"} className="absolute z-[2] bg-white text-headertext/90 border-[0.15rem] border-white rounded-full w-[6rem] h-[6rem] mt-[4.3rem]"
                        />
                }

                <div className="px-[1.19rem] w-full">
                    <div className="relative flex flex-col items-center mt-[7.5rem] pt-[3.48rem] text-center w-full pb-[1.69rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div
                            onClick={() => router.push('/u/login')}
                            className="absolute top-[1.31rem] right-[1.31rem] flex justify-center items-center w-[3.13rem] h-[3.13rem] rounded-full bg-grey"
                        >
                            <img
                                className="cursor-pointer w-[1.25rem] h-[1.25rem]"
                                src="/settings.svg"
                            />
                        </div>
                        <div className="text-headertext font-semibold text-[1.125rem]">
                            {userProfile?.firstName} {userProfile?.lastName}
                        </div>
                        <div className="text-placeholder font-medium mt-[0.19rem] text-[0.9375rem]">
                            {userProfile?.role}
                            {/* , <br />
                            {userProfile?.company} */}
                        </div>
                        <div className="flex gap-[0.31rem] items-center text-subtext mt-[1.31rem]">
                            <img
                                src="/small-phone.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{userProfile?.phoneNumber}</div>
                        </div>
                        <div className="flex gap-[0.38rem] items-center text-subtext mt-[0.69rem]">
                            <img
                                src="/small-envelope.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{userProfile?.emailAddress}</div>
                        </div>
                    </div>
                </div>

                <div className="flex-1 pb-[6.31rem] overflow-y-auto hide-scrollbar flex flex-col w-full mt-[0.75rem] gap-[0.75rem]">

                    <div className="flex flex-col gap-[0.38rem] px-[0.63rem] mx-[1.19rem] pt-[0.75rem] pb-[0.65rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div className="text-headertext">Social networks</div>
                        <div className="flex gap-[0.63rem]">
                            {
                                (userProfile?.links && Object.keys(userProfile?.links).length !== 0) ? Object.entries(userProfile?.links).map(([key, value], index) => (
                                    (
                                        <>
                                            {userProfile?.links[key] &&
                                                <a
                                                    key={index}
                                                    href={typeof value === "string" ? (value.startsWith("http") ? value : `https://${value}`) : undefined}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="cursor-pointer h-[2.54rem] w-[2.54rem] bg-outline/50 rounded-full flex items-center justify-center"
                                                >
                                                    {icons[key as keyof typeof icons]}
                                                </a>

                                            }
                                        </>
                                    )
                                    // <img key={index} onClick={() => router.push(value)} src={`/${key}.svg`} className="h-[2.54rem] w-[2.54rem]" />
                                )) :
                                    <div className="cursor-pointer text-[0.85rem] font-medium text-subtext h-[2.54rem] flex items-center justify-center">
                                        No links added
                                    </div>
                            }
                        </div>
                    </div>
                    <div className="flex flex-col mx-[1.19rem] gap-[0.75rem]">
                        {
                            userProfile?.hasTestimonial && <ContactNav
                                onClick={() => router.push(`/u/testimonials/${getUserId()}`)}
                                text="Testimonials"
                            />
                        }
                        {
                            userProfile?.hasSocialProof && <ContactNav
                                onClick={() => router.push(`/u/social-proof/${getUserId()}`)}
                                text="Social proof"
                            />
                        }
                        {userProfile?.website && (
                            <a
                                href={userProfile.website.startsWith("http") ? userProfile.website : `https://${userProfile.website}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <ContactNav text="Website" hasArrow={false} />
                            </a>
                        )}
                    </div>

                    {
                        !loadingMap && (mapEmbedUrl ?
                            <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                                <div>
                                    <div className="text-headertext">Location (Google Maps)</div>
                                    <div className="flex items-center gap-1 mt-1">
                                        <HiLocationMarker size={24} color={colors.headertext} />
                                        {
                                            address &&
                                            <span className={"text-sm text-headertext"}>{formatAddress(address as Address)}</span>
                                        }
                                    </div>
                                </div>
                                <div className="h-[11.19rem] w-full">
                                    <iframe
                                        className="w-full h-full rounded-[0.625rem]"
                                        src={mapEmbedUrl}
                                        allowFullScreen
                                        loading="lazy"
                                    ></iframe>
                                </div>
                            </div>
                            :
                            <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                                <div>
                                    <div className="text-headertext">Location (Google Maps)</div>
                                    <div className="flex items-center gap-1 mt-1">
                                        <HiLocationMarker size={24} color={colors.headertext} />
                                        <div className="text-[0.85rem] font-semibold text-center text-red-500">
                                            Location not provided
                                        </div>
                                    </div>
                                </div>
                            </div>)
                    }

                    {
                        loadingMap &&
                        <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                            <div>
                                <div className="text-headertext">Location (Google Maps)</div>
                                <div className="flex items-center gap-1 mt-1">
                                    <HiLocationMarker size={24} color={colors.headertext} />
                                    <div className="text-[0.85rem] font-semibold text-center text-gray-500">
                                        Loading...
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    <div className="bg-white pt-[1.44rem] pb-[1.13rem] text-placeholder text-center text-[0.75rem]">
                        Copyright {new Date().getFullYear()} BeyondCard
                    </div>
                </div>

                <div className="flex h-[6.31rem] fixed bottom-0 bg-white w-full justify-center items-center shadow-lg gap-[0.62rem]">
                    <div
                        onClick={() => {
                            generateVCF(userProfile);
                            setTimeout(() => { setIsOpen(true) }, 5000)
                        }}
                        className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center w-[10.4rem] h-[2.75rem] rounded-[0.55125rem]"
                    >
                        Save contact
                    </div>
                    <div
                        onClick={() => setIsOpen(true)}
                        className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center w-[10.4rem] h-[2.75rem] rounded-[0.55125rem]"
                    >
                        Exchange contact
                    </div>
                </div>
            </div>
        </div>
    );
}
