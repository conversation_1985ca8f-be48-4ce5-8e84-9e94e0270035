"use client";

import client from "@/api/client";
import { getBearerToken, getUserId, setContactId } from "@/app/tokenStorage";
import { useNavigationStore } from "@/store/navigationStore";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { SocialProof } from "../../../../../interfaces";
import { SocialProofCard } from "@/components/SocialProofCard";

export default function SocialProofComponent() {
    const router = useRouter();
    const { id } = useParams();
    const { data, setData } = useNavigationStore();

    console.log("DATA", data.contactData)

    const { data: socialProofs, isLoading, error } = useQuery({
        queryKey: ["social-proofs", id || data?.contactData?.userId],
        queryFn: async () => {
            // If we have an ID param, use it and set contact data
            if (id) {
                setContactId(id as string);
                const profileResponse = await client.post(`/profile/view`, {
                    userId: parseInt(id as string)
                });
                setData({ contactData: profileResponse.data.data });
                const response = await client.get(`/social/${id}`);
                return response.data.data as SocialProof[];
            }
            // Otherwise use data from navigation store
            else if (data?.contactData?.userId) {
                const response = await client.get(`/social/${data.contactData.userId}`);
                console.log("RESPONSE:", response)
                return response.data.data as SocialProof[];
            }
            return [];
        },
        enabled: !!(id || data?.contactData?.userId),
    });

    if (isLoading) {
        return (
            <div className="w-screen h-screen px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[10rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        )
    }

    return (
        <div className="flex relative flex-col w-full h-screen items-center">
            <div className="flex flex-col bg-white w-full max-w-[30rem] mx-auto shadow-lg border-b-[0.3rem] border-gray-300 pt-[1.94rem] pb-[0.76rem] px-[1.06rem]">
                <div
                    onClick={() => router.back()}
                    className="flex justify-center cursor-pointer bg-grey items-center w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem]"
                >
                    <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" />
                    <div className="text-placeholder text-[0.875rem]">Go back</div>
                </div>

                <div className="mt-[1.56rem] text-headertext px-[0.44rem]">Social proof</div>
            </div>

            <div className="flex w-full max-w-[23rem] mx-auto flex-col bg-altbg2 flex-1 px-[1rem] mt-[0.75rem] gap-[0.88rem] pb-[3.38rem] overflow-y-auto hide-scrollbar">
                {(socialProofs && socialProofs?.length > 0) ? socialProofs.map((proof, index) => (
                    <SocialProofCard key={index} viewMode proof={proof} />
                )) : isLoading ?
                    <div className="flex-1 px-[1rem] flex items-center justify-center">
                        <img className="w-full max-w-[8.5rem] object-contain animate-bounce" src="/Logo.svg" />
                    </div>
                    :
                    <div className="text-subtext text-sm flex-1 flex justify-center items-center">Nothing to see</div>
                }
            </div>
        </div>
    );
}
