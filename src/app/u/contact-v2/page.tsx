"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { HiLocationMarker } from 'react-icons/hi';
import colors from "../../../../themePalette";
import ContactNav from "@/components/ContactNav";
import { BsInstagram, BsLinkedin, BsTiktok, BsTwitterX, BsWhatsapp } from "react-icons/bs";
import { FaFacebook } from "react-icons/fa";

export default function Home() {
    const [isOpen, setIsOpen] = useState(false);
    const router = useRouter();

    // Centralized Contact Information
    const contactInfo = {
        firstName: "Daniel A.",
        lastName: "Kwarteng",
        jobTitle: "Product Manager Consultant",
        company: "Recharge Company Limited",
        phone: "+233 54 079 1011",
        email: "<EMAIL>",
        location: "Accra, Ghana",
        website: "",
        photoUrl: "https://beyond-delta.vercel.app/dka.jpg",
        socialLinks: {
            "twitter": "twitter.com/danielkwarteng",
            "linked-in": "https://www.linkedin.com/in/daniel-agyekum-kwarteng-843b67127/",
            "instagram": "https://www.instagram.com/danielkwarteng_/",
            "facebook": "facebook.com/daniel.kwarte",
            "tiktok": "facebook.com/daniel.kwarte",
            "whatsapp": "wa.me/233540791011",
        },
    };

    const icons = {
        "twitter": <BsTwitterX />,
        "linked-in": <BsLinkedin />,
        "instagram": <BsInstagram />,
        "facebook": <FaFacebook />,
        "tiktok": <BsTiktok />,
        "whatsapp": <BsWhatsapp />
    }

    const generateVCF = () => {
        const vcfContent = `BEGIN:VCARD
VERSION:3.0
N:${contactInfo.lastName};${contactInfo.firstName};;;
FN:${contactInfo.firstName} ${contactInfo.lastName}
ORG:${contactInfo.company}
TITLE:${contactInfo.jobTitle}
TEL:${contactInfo.phone.replaceAll(" ", "")}
EMAIL:${contactInfo.email}
ADR:;;${contactInfo.location}
PHOTO;VALUE=URL:${contactInfo.photoUrl}
URL;TYPE=WORK:${contactInfo.website || "https://example.com"}
X-TWITTER:${contactInfo.socialLinks.twitter}
X-LINKEDIN:${contactInfo.socialLinks["linked-in"]}
X-INSTAGRAM:${contactInfo.socialLinks.instagram}
X-FACEBOOK:${contactInfo.socialLinks.facebook}
END:VCARD`;

        const blob = new Blob([vcfContent], { type: "text/vcard" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${contactInfo.firstName}.vcf`;
        a.click();
        URL.revokeObjectURL(url);
    };

    return (
        <div className="flex relative flex-col w-full max-w-[52.81rem] mx-auto bg-altbg items-center">
            <Modal isOpen={isOpen}>
                <div className="flex flex-col justify-center px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => setIsOpen(false)}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold text-center">
                            Exchange contact
                        </div>
                        <div className="flex flex-col gap-[0.43rem] mt-[1.06rem]">
                            <AppTextInput label="Your name" placeholder="Enter name" />
                            <AppTextInput label="Email" placeholder="Enter email" />
                            <AppTextInput label="Phone" placeholder="Enter phone" />
                            <AppTextInput label="Job title" placeholder="Enter job title" />
                            <AppTextInput label="Company" placeholder="Enter company" />
                            {/* <AppTextArea label="Leave message" placeholder="Enter message" /> */}
                        </div>
                        <div
                            onClick={() => setIsOpen(false)}
                            className="h-[3.13rem] mt-[1.6rem] rounded-[0.625rem] flex cursor-pointer font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center"
                        >
                            Exchange contact
                        </div>
                    </div>
                </div>
            </Modal>

            <div
                className="h-[14.38rem] w-full bg-cover"
                style={{ backgroundImage: "url('/contact-bg.svg')" }}
            ></div>

            <div className="flex max-w-[52.81rem] mx-auto flex-col w-full absolute items-center w-screen h-screen">
                <div
                    className="absolute z-[2] border-[0.15rem] border-white rounded-full w-[6rem] h-[6rem] mt-[4.3rem] bg-cover"
                    style={{ backgroundImage: "url('/dka.jpg')" }}
                ></div>

                <div className="px-[1.19rem] w-full">
                    <div className="relative flex flex-col items-center mt-[7.5rem] pt-[3.48rem] text-center w-full pb-[1.69rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div
                            onClick={() => router.push('/u/login')}
                            className="absolute top-[1.31rem] right-[1.31rem] flex justify-center items-center w-[3.13rem] h-[3.13rem] rounded-full bg-grey"
                        >
                            <img
                                className="cursor-pointer w-[1.25rem] h-[1.25rem]"
                                src="/settings.svg"
                            />
                        </div>
                        <div className="text-headertext font-semibold text-[1.125rem]">
                            {contactInfo.firstName} {contactInfo.lastName}
                        </div>
                        <div className="text-placeholder font-medium mt-[0.19rem] text-[0.9375rem]">
                            {contactInfo.jobTitle}, <br />
                            {contactInfo.company}
                        </div>
                        <div className="flex gap-[0.31rem] items-center text-subtext mt-[1.31rem]">
                            <img
                                src="/small-phone.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{contactInfo.phone}</div>
                        </div>
                        <div className="flex gap-[0.38rem] items-center text-subtext mt-[0.69rem]">
                            <img
                                src="/small-envelope.svg"
                                className="h-[0.81rem] w-[0.81rem]"
                            />
                            <div className="text-[0.9375rem]">{contactInfo.email}</div>
                        </div>
                    </div>
                </div>

                <div className="flex-1 pb-[6.31rem] overflow-y-auto hide-scrollbar flex flex-col w-full mt-[0.75rem] gap-[0.75rem]">

                    <div className="flex flex-col gap-[0.38rem] px-[0.63rem] mx-[1.19rem] pt-[0.75rem] pb-[0.65rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div className="text-headertext">Social networks</div>
                        <div className="flex gap-[0.63rem]">
                            {
                                Object.entries(contactInfo.socialLinks).map(([key, value], index) => (
                                    <div key={index} onClick={() => router.push(value)} className="cursor-pointer h-[2.54rem] w-[2.54rem] bg-outline/50 rounded-full flex items-center justify-center">
                                        {icons[key as keyof typeof icons]}
                                    </div>
                                    // <img key={index} onClick={() => router.push(value)} src={`/${key}.svg`} className="h-[2.54rem] w-[2.54rem]" />
                                ))
                            }
                        </div>
                    </div>
                    <div className="flex flex-col mx-[1.19rem] gap-[0.75rem]">
                        <ContactNav
                            onClick={() => router.push("/u/product-catalog")}
                            text="Product catalog"
                        />
                        <ContactNav
                            onClick={() => router.push("/u/service-catalog")}
                            text="Service catalog" />
                        <ContactNav
                            onClick={() => router.push("/u/testimonials")}
                            text="Testimonials"
                        />
                        <ContactNav
                            onClick={() => router.push("/u/social-proof")}
                            text="Social proof"
                        />
                        <ContactNav text="Website" hasArrow={false} />
                    </div>

                    <div className="mx-[1.19rem] flex flex-col gap-[0.56rem] px-[0.56rem] pt-[0.94rem] pb-[0.69rem] bg-white rounded-[0.625rem] shadow-lg">
                        <div>
                            <div className="text-headertext">Location (Google Maps)</div>
                            <div className="flex items-center gap-1 mt-1">
                                <HiLocationMarker size={24} color={colors.headertext} />
                                <span className={"text-sm text-headertext"}>Accra, Ghana</span>
                            </div>
                        </div>
                        <div className="h-[11.19rem] w-full">
                            <iframe
                                className="w-full h-full rounded-[0.625rem]"
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.835434509374!2d144.9630579153163!3d-37.81410797975159!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad642af0f11fd81%3A0x5045675218ce6e0!2sFederation%20Square%2C%20Melbourne%20VIC%2C%20Australia!5e0!3m2!1sen!2sgh!4v1690288112336!5m2!1sen!2sgh"
                                allowFullScreen
                                loading="lazy"
                            ></iframe>
                        </div>
                    </div>

                    <div className="bg-white pt-[1.44rem] pb-[1.13rem] text-placeholder text-center text-[0.75rem]">
                        Copyright 2024 BeyondCard
                    </div>
                </div>

                <div className="flex h-[6.31rem] fixed bottom-0 bg-white w-full justify-center items-center shadow-lg gap-[0.62rem]">
                    <div
                        onClick={generateVCF}
                        className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center w-[10.4rem] h-[2.75rem] rounded-[0.55125rem]"
                    >
                        Save contact
                    </div>
                    <div
                        onClick={() => setIsOpen(true)}
                        className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center w-[10.4rem] h-[2.75rem] rounded-[0.55125rem]"
                    >
                        Exchange contact
                    </div>
                </div>
            </div>
        </div>
    );
}
