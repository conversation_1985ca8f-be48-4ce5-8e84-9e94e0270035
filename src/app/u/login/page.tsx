"use client";

import { useMutation } from "@tanstack/react-query";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import { useState } from "react";
import client from "@/api/client";
import FormikAppTextInput from "@/components/formik/FormikAppTextInput";
import FormikAppSubmit from "@/components/formik/FormikAppSubmit";
import { decodeToken, setToken, setUserId } from "@/app/tokenStorage";
import GoBackButton from "@/components/GoBackButton";

const validationSchema = Yup.object({
  emailAddress: Yup.string().email("Invalid email address").required("Email is required"),
  password: Yup.string().required("Password is required"),
});

export default function Home() {
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      setErrorMessage("");
      return client.post("/login", values);
    },
    onSuccess: (response) => {
      console.log("Login successful:", response.data);
      console.log("User ID:", response?.data?.data?.id);
      // Retrieve token from the response headers.
      const token =
        response.headers["authorization"]
      if (token) {
        console.log(token)
        console.log(decodeToken(token))
        setToken(token);
        setUserId(response?.data?.data?.id)
        // Navigate to the dashboard
        router.push("/dashboard");
      }
    },
    onError: (error: any) => {
      console.error("Login failed:", error);
      setErrorMessage(
        error?.response?.data?.message || "Login failed. Please try again."
      );
    },
  });


  return (
    <>
      <div className="flex items-center sticky top-0 z-[10] bg-white h-[5.56rem] px-[1rem] sm:px-[4.38rem] border-b border-outline">
        <img src={"/Logo.svg"} className="h-[1.79rem] w-[7.5rem]" />
      </div>
      <div className="flex flex-col items-center">
        <div className="mr-auto mt-[1rem] max-w-[32.31rem] px-[1rem] sm:px-[4.38rem] w-full"><GoBackButton /></div>
        <div className="w-full max-w-[32.31rem] px-[2.69rem] pt-[1.69rem] pb-[2.25rem] bg-white rounded-[0.625rem] sm:shadow-lg">
          <div className="text-[1.375rem] font-medium mb-[1.94rem]">Log into your account</div>

          {errorMessage && (
            <div className="border border-red-500 bg-red-100 text-red-500 text-center px-[1rem] py-[0.5rem] rounded-lg font-semibold mb-4">
              {errorMessage}
            </div>
          )}

          <Formik
            initialValues={{ emailAddress: "", password: "" }}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting }) => {
              mutation.mutate(values, {
                onSettled: () => setSubmitting(false), // Ensures Formik stops submitting
              });
            }}
          >
            {({ isSubmitting }) => (
              <Form>
                <div className="flex flex-col gap-[1.12rem]">
                  <FormikAppTextInput name="emailAddress" label="Email" placeholder="Enter email address" type="email" />
                  <FormikAppTextInput name="password" label="Password" placeholder="Enter password" type="password" />
                </div>

                <div onClick={() => router.push('/forgot-password')} className="flex cursor-pointer hover:font-semibold justify-end text-green font-medium text-[0.9375rem] mt-[1rem]">
                  Forgot password?
                </div>

                <FormikAppSubmit
                  text="Login"
                  widthClass="w-full mt-[0.62rem]"
                  disabled={isSubmitting || mutation.isPending}
                />
              </Form>
            )}
          </Formik>
        </div>

        <div className="mt-[1.68rem] text-[0.9375rem] font-medium gap-1 flex">
          <div className="text-subtext">{"Don't have an account?"}</div>
          <div onClick={() => router.push('/sign-up')} className="cursor-pointer hover:underline">
            {"Sign up"}
          </div>
        </div>
      </div>
    </>
  );
}
