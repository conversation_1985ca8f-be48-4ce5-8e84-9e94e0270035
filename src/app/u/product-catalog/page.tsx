"use client";

import ProductListItem from "@/components/ProductListItem";
import { useRouter } from "next/navigation";
import { BiCart, BiCheckCircle, BiCoinStack } from "react-icons/bi";
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '@/store/store';
import { addItem, decrementItem, setItemQuantity, removeItem, clearCart } from '@/store/features/cartSlice';
import Modal from "@/components/Modal";
import { useState } from "react";
import { MdMoney } from "react-icons/md";
import AppTextInput from "@/components/AppTextInput";
import AppTextArea from "@/components/AppTextArea";
import ButtonSelect from "@/components/ButtonSelect";

const products = [
    {
        id: 1,
        imageSrc: `https://picsum.photos/600/670?random=1`,
        title: "Agile Playbook Template",
        price: 250
    },
    {
        id: 2,
        imageSrc: `https://picsum.photos/600/670?random=2`,
        title: "Product Roadmap Template",
        price: 200
    },
    {
        id: 3,
        imageSrc: `https://picsum.photos/600/670?random=3`,
        title: "Market Research Report Sample",
        price: 300
    },
    {
        id: 4,
        imageSrc: `https://picsum.photos/600/670?random=4`,
        title: "User Persona Template Pack",
        price: 150
    },
    {
        id: 5,
        imageSrc: `https://picsum.photos/600/670?random=5`,
        title: "Metrics Dashboard Template",
        price: 400
    },
    {
        id: 6,
        imageSrc: `https://picsum.photos/600/670?random=6`,
        title: "Product Launch Checklist",
        price: 350
    },
    {
        id: 7,
        imageSrc: `https://picsum.photos/600/670?random=7`,
        title: "Competitor Analysis Toolkit",
        price: 300
    },
    {
        id: 8,
        imageSrc: `https://picsum.photos/600/670?random=8`,
        title: "Customer Feedback Loop Guide",
        price: 200
    },
    {
        id: 9,
        imageSrc: `https://picsum.photos/600/670?random=9`,
        title: "Presentation Deck for PMs",
        price: 500
    },
    {
        id: 10,
        imageSrc: `https://picsum.photos/600/670?random=10`,
        title: "Annual Product Review Template",
        price: 450
    }
];

const specialOffers = [
    {
        id: 11,
        imageSrc: `https://picsum.photos/600/670?random=11`,
        title: "Agile Playbook Template",
        price: 250,
        oldPrice: 300
    },
    {
        id: 12,
        imageSrc: `https://picsum.photos/600/670?random=12`,
        title: "Product Roadmap Template",
        price: 200,
        oldPrice: 250
    },
    {
        id: 13,
        imageSrc: `https://picsum.photos/600/670?random=13`,
        title: "Market Research Report Sample",
        price: 300,
        oldPrice: 350
    },
    {
        id: 14,
        imageSrc: `https://picsum.photos/600/670?random=14`,
        title: "User Persona Template Pack",
        price: 150,
        oldPrice: 200
    },
    {
        id: 15,
        imageSrc: `https://picsum.photos/600/670?random=15`,
        title: "Metrics Dashboard Template",
        price: 400,
        oldPrice: 450
    },
    {
        id: 16,
        imageSrc: `https://picsum.photos/600/670?random=16`,
        title: "Product Launch Checklist",
        price: 350,
        oldPrice: 400
    },
    {
        id: 17,
        imageSrc: `https://picsum.photos/600/670?random=17`,
        title: "Competitor Analysis Toolkit",
        price: 300,
        oldPrice: 375
    },
    {
        id: 18,
        imageSrc: `https://picsum.photos/600/670?random=18`,
        title: "Customer Feedback Loop Guide",
        price: 200,
        oldPrice: 240
    },
    {
        id: 19,
        imageSrc: `https://picsum.photos/600/670?random=19`,
        title: "Presentation Deck for PMs",
        price: 500,
        oldPrice: 600
    },
    {
        id: 20,
        imageSrc: `https://picsum.photos/600/670?random=20`,
        title: "Annual Product Review Template",
        price: 450,
        oldPrice: 500
    }
];


const NAVS = {
    products: "Products",
    special_offers: "Special offers"
}

const navs = Object.values(NAVS)

export default function ProductCatalog() {
    const router = useRouter();
    const cart = useSelector((state: RootState) => state.cart);
    const [isCartVisible, setIsCartVisible] = useState(false);
    const [enquiryModalOpen, setEnquiryModalOpen] = useState(false);
    const [buyModalOpen, setBuyModalOpen] = useState(false);
    const [selectedNav, setSelectedNav] = useState(navs[0]);
    const [selectedReceiverOption, setSelectedReceiverOption] = useState<string>('myself');

    const options = [
        { label: 'Myself', value: 'myself' },
        { label: 'Someone Else', value: 'someone_else' },
    ];

    const dispatch = useDispatch();

    // Array of product objects

    return (
        <div className="flex relative flex-col w-full h-screen items-center">
            <Modal isOpen={isCartVisible}>
                <div className="flex flex-col justify-start pt-10 px-[0.5rem] items-center w-screen h-screen bg-black/50 backdrop-blur-lg overflow-y-auto hide-scrollbar">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => setIsCartVisible(false)}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold mb-5">
                            Cart
                        </div>
                        <div className="flex flex-col gap-3">
                            {
                                cart.totalQuantity > 0 ?
                                    <>
                                        {
                                            cart.items.map((item, index) => (
                                                <div key={index} className="flex border border-outline shadow rounded-[0.625rem] items-center px-[0.5rem] py-[0.4rem] gap-2">
                                                    <img className="w-[3.2rem] border border-outline h-[3.56rem] rounded-[1rem] object-cover" src={products.find((product) => product.id == item.id)?.imageSrc} alt={item.name} />
                                                    <div className="flex gap-2 flex-col justify-center">
                                                        <div className="font-bold">
                                                            {item.name}
                                                        </div>
                                                        <div className="flex items-center">
                                                            <div onClick={() => dispatch(decrementItem(item.id))} className="flex cursor-pointer items-center justify-center w-[1.3rem] h-[1.3rem] border-2 border-outline rounded-l-lg">-</div>
                                                            <div className="flex items-center font-semibold text-[0.85rem] justify-center px-2 h-[1.3rem] bg-grey">{item.quantity}</div>
                                                            <div className="flex items-center font-semibold text-[0.85rem] justify-center px-2 h-[1.3rem] bg-grey">GH¢{parseFloat(`${item.price * item.quantity}`).toFixed(2)}</div>
                                                            <div onClick={() => dispatch(addItem({ id: item.id, name: item.name, price: item.price }))} className="flex cursor-pointer items-center justify-center w-[1.3rem] h-[1.3rem] border-2 border-outline rounded-r-lg">+</div>
                                                        </div>

                                                    </div>

                                                    <img onClick={() => dispatch(removeItem(item.id))} src="/trash-red.svg" className="ml-auto w-[1.2rem] h-[1.2rem]" />
                                                </div>
                                            ))
                                        }

                                        <div className="bg-[#F8F8F8] rounded-[0.625rem] border border-outline py-2 px-2 flex items-center">
                                            <BiCoinStack className="text-green font-medium w-5 h-5" />
                                            <div className="text-placeholder font-semibold ml-2 flex items-center justify-center">
                                                Total price:
                                            </div>
                                            <div className="text-green text-sm font-bold ml-auto flex items-center justify-center">
                                                GH¢{parseFloat(`${cart.totalPrice}`).toFixed(2)}
                                            </div>
                                        </div>

                                        <div className="flex justify-center items-center gap-[0.62rem]">
                                            <div onClick={() => { setEnquiryModalOpen(true); setIsCartVisible(false); }}
                                                className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                                            >
                                                Make an enquiry
                                            </div>
                                            <div onClick={() => { setBuyModalOpen(true); setIsCartVisible(false); }}
                                                className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                                            >
                                                Buy
                                            </div>
                                        </div>
                                    </>
                                    :
                                    <div className="py-5 text-subtext text-center text-medium">Cart empty</div>
                            }
                        </div>
                    </div>
                </div>
            </Modal>

            <Modal isOpen={enquiryModalOpen}>
                <div className="flex flex-col pt-10 px-[0.5rem] items-center w-screen h-screen overflow-y-auto hide-scrollbar bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => { setIsCartVisible(false); setEnquiryModalOpen(false); }}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Leave enquiry
                        </div>
                        <div className="flex flex-col gap-[0.43rem] mt-[1.25rem]">
                            <AppTextInput label="Your name" placeholder="Enter name" />
                            <AppTextInput label="Email" placeholder="Enter email" />
                            <AppTextInput label="Phone" placeholder="Enter phone" />
                            <AppTextArea label="Question" placeholder="Ask a question" />
                        </div>

                        <div className="flex mt-[1.6rem] justify-center items-center gap-[0.62rem]">
                            <div onClick={() => { setEnquiryModalOpen(false); setIsCartVisible(true); }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-grey items-center bg-grey text-black justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Go back
                            </div>
                            <div onClick={() => { }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Enquire now
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            <Modal isOpen={buyModalOpen}>
                <div className="flex flex-col pt-10 px-[0.5rem] items-center w-screen h-screen overflow-y-auto hide-scrollbar bg-black/50 backdrop-blur-lg">
                    <div className="flex flex-col max-h-[75vh] overflow-y-auto w-full max-w-[52.81rem] mx-auto rounded-[1.25rem] px-[1rem] pb-[1.31rem] bg-white">
                        <img
                            onClick={() => { setIsCartVisible(false); setBuyModalOpen(false); }}
                            src="/cross-small.svg"
                            className="h-[0.88rem] w-[0.88rem] mt-[0.69rem] ml-auto mr-[0.38rem]"
                        />
                        <div className="mt-[0.56rem] text-[1.125rem] font-semibold">
                            Complete Order
                        </div>
                        <div className="flex flex-col overflow-y-auto gap-[0.43rem] mt-[1.25rem]">
                            <ButtonSelect options={options} value={selectedReceiverOption} label="Who will receive this item?" onChange={(value) => { setSelectedReceiverOption(value) }} />
                            <div className="font-semibold mt-5 text-[1rem] text-headertext mb-3">
                                Your Details
                            </div>
                            <div className="pl-2">
                                <AppTextInput
                                    label="Full name"
                                    placeholder="Enter full name"
                                />
                                <AppTextInput
                                    label="WhatsApp number"
                                    placeholder="Enter WhatsApp number"
                                />
                                <AppTextInput
                                    label="Email address"
                                    labelExtra="Optional"
                                    placeholder="Enter email address"
                                />
                            </div>

                            {
                                selectedReceiverOption === "someone_else" &&
                                <>
                                    <div className="font-semibold mt-5 text-[1rem] text-headertext mb-3">
                                        Receiver's Details
                                    </div>
                                    <div className="pl-2">
                                        <AppTextInput
                                            label="Full name"
                                            placeholder="Enter full name"
                                        />
                                        <AppTextInput
                                            label="WhatsApp number"
                                            placeholder="Enter WhatsApp number"
                                        />
                                        <AppTextInput
                                            label="Email address"
                                            labelExtra="Optional"
                                            placeholder="Enter email address"
                                        />
                                    </div>
                                </>
                            }

                        </div>

                        <div className="flex mt-[1.6rem] justify-center items-center gap-[0.62rem]">
                            <div onClick={() => { setBuyModalOpen(false); setIsCartVisible(true); }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium border-[0.055rem] border-grey items-center bg-grey text-black justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Go back
                            </div>
                            <div onClick={() => { }}
                                className="flex cursor-pointer text-[0.881875rem] font-medium bg-black text-white border-[0.055rem] border-black items-center justify-center flex-1 h-[2.75rem] rounded-[0.55125rem]"
                            >
                                Make order
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            <div className="flex flex-col bg-white w-full max-w-[52.81rem] mx-auto shadow-lg border-b-[0.1rem] border-outline pt-[2.38rem] px-[1.06rem]">
                <div className="flex items-center justify-between mb-[1.62rem]">
                    <div
                        onClick={() => { router.back(); }}
                        className="flex justify-center cursor-pointer bg-grey items-center w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem] cursor-pointer"
                    >
                        <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" alt="Go back" />
                        <div className="text-placeholder text-[0.875rem]">Go back</div>
                    </div>

                    <div onClick={() => setIsCartVisible(true)} className="relative">
                        {cart.totalQuantity > 0 && <div className="absolute z-[1] top-1 right-0 flex w-4 h-4 items-center justify-center text-white bg-rose-500 font-medium text-[0.7rem] rounded-full">{cart.totalQuantity}</div>}
                        <BiCart className="text-4xl text-headertext" />
                    </div>
                </div>

                <div className="flex mx-[2.07rem] justify-between">
                    {navs.map((nav, index) => (
                        <div key={index} onClick={() => { setSelectedNav(nav) }} className={`${selectedNav === nav ? 'text-green border-b-[0.08rem] border-green' : 'border-transparent text-placeholder'} duration-200 transition-all cursor-pointermax-w-[52.81rem] mx-auto text-[0.875rem] pb-[0.45rem]`}>{nav}</div>
                    ))}
                </div>
            </div>

            <div className="flex flex-col bg-white w-full max-w-[52.81rem] mx-auto shadow-lg flex-1 px-[0.94rem] pt-[0.94rem] pb-[3.19rem] gap-[0.51rem] overflow-y-auto hide-scrollbar">
                <div className="grid grid-cols-2 gap-x-[0.62rem] gap-y-[1.94rem]">
                    {(selectedNav === NAVS.products ?
                        products.map(product => (
                            <ProductListItem
                                key={product.id}
                                id={product.id}
                                imageSrc={product.imageSrc}
                                title={product.title}
                                price={product.price}
                            />
                        )) :
                        specialOffers.map(product => (
                            <ProductListItem
                                key={product.id}
                                id={product.id}
                                imageSrc={product.imageSrc}
                                title={product.title}
                                price={product.price}
                                oldPrice={product.oldPrice}
                            />
                        ))
                    )}
                </div>
            </div>
        </div>
    );
}
