"use client";

import client from "@/api/client";
import { getBearerToken, setContactId } from "@/app/tokenStorage";
import { useNavigationStore } from "@/store/navigationStore";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { obscureEmail, trimmed } from "@/utils/helpers";
import { format } from "date-fns";
import { Testimonial } from "../../../../../interfaces";

export default function Testimonials() {
    const router = useRouter();
    const { id } = useParams();
    const { data, setData } = useNavigationStore();

    const { data: testimonials, isLoading, error } = useQuery({
        queryKey: ["testimonials", id || data?.contactData?.userId],
        queryFn: async () => {
            // If we have an ID param, use it and set contact data
            if (id) {
                setContactId(id as string);
                const profileResponse = await client.post(`/profile/view`, {
                    userId: parseInt(id as string)
                });
                setData({ contactData: profileResponse.data.data });
                const response = await client.get(`/testimonials/user/approved/${id}`);
                return response.data.data as Testimonial[];
            }
            // Otherwise use data from navigation store
            else if (data?.contactData?.userId) {
                const response = await client.get(`/testimonials/user/approved/${data.contactData.userId}`);
                return response.data.data as Testimonial[];
            }
            return [];
        },
        enabled: !!(id || data?.contactData?.userId),
    });

    if (isLoading) {
        return (
            <div className="w-screen h-screen px-[1rem] flex items-center justify-center">
                <img className="w-full max-w-[10rem] object-contain animate-bounce" src="/Logo.svg" />
            </div>
        )
    }

    return (
        <div className="flex relative flex-col w-full h-screen items-center">
            <div className="flex flex-col bg-white w-full max-w-[30rem] mx-auto shadow-lg border-b-[0.3rem] border-gray-300 pt-[2.38rem] pb-[1.3rem] px-[1.06rem]">
                <div
                    onClick={() => router.back()}
                    className="flex justify-center cursor-pointer bg-grey items-center w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem]"
                >
                    <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" />
                    <div className="text-placeholder text-[0.875rem]">Go back</div>
                </div>

                <div className="mt-[1.12rem] text-center font-semibold text-[1.75rem]">
                    My <br /> Testimonials
                </div>
            </div>

            <div className="flex flex-col w-full max-w-[28rem] mx-auto bg-altbg2 flex-1 px-[1rem] pt-[0.5rem] pb-[3.19rem] gap-[0.51rem] overflow-y-auto hide-scrollbar">
                {
                    testimonials && testimonials.length > 0 ? testimonials.map((testimonial, index) => (
                        <div
                            key={index}
                            className="flex relative flex-col shadow bg-white border-[1px] border-outline rounded-[0.46rem] pl-[0.66rem] pr-[0.56rem] pt-[1.67rem] pb-[1.34rem]"
                        >
                            <img src="/quotes.svg" className="absolute z-[1] w-[4rem] top-[1rem] left-[0.625rem]" />
                            <div className="z-[2] text-[0.875rem] text-placeholder">{testimonial.testimonial}</div>
                            <div className="z-[2] mt-[0.63rem] text-[0.9375rem] font-bold text-headertext">{testimonial.writerName}</div>
                            <div className="z-[2] text-[0.875rem] text-placeholder">{testimonial.writerRole}{", "}{testimonial.writerCompany}</div>
                            <div className="text-[0.75rem] font-medium text-subtext">
                                Approved by {trimmed(testimonial?.writerName).split(" ")[0]}{" "}
                                {obscureEmail(testimonial?.emailAddress, testimonial?.emailVisibility)
                                    ? `(${obscureEmail(
                                        testimonial?.emailAddress,
                                        testimonial?.emailVisibility
                                    )})`
                                    : ""}{" "}
                                {testimonial?.approvalTime
                                    ? `on ${format(new Date(testimonial.approvalTime), "MMM d, yyyy")}`
                                    : ""}
                            </div>
                        </div>
                    ))
                        : isLoading ?
                            <div className="flex-1 px-[1rem] flex items-center justify-center">
                                <img className="w-full max-w-[8.5rem] object-contain animate-bounce" src="/Logo.svg" />
                            </div>
                            :
                            <div className="text-subtext text-sm flex-1 flex justify-center items-center">Nothing to see</div>
                }
            </div>
        </div>
    );
}
