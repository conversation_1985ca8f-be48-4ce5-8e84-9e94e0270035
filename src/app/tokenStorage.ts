// tokenStorage.ts

import * as jwt_decode from 'jwt-decode';

const TOKEN_KEY = 'app_token';
const USER_ID = 'app_user_id';
const CONTACT_ID = 'app_contact_id';

/**
 * Stores the user_id in local storage.
 * @param user_id - The user_id string to be saved.
 */
export function setUserId(user_id: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(USER_ID, user_id);
  }
}

/**
 * Stores the user_id in local storage.
 * @param contact_id - The user_id string to be saved.
 */
export function setContactId(contact_id: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(CONTACT_ID, contact_id);
  }
}

/**
 * Stores the token in local storage.
 * @param token - The token string to be saved.
 */
export function setToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(TOKEN_KEY, token);
  }
}

/**
 * Retrieves the user_id from local storage.
 * @returns The user_id string if it exists, otherwise null.
 */
export function getContactId(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(CONTACT_ID);
  }
  return null;
}

/**
 * Retrieves the user_id from local storage.
 * @returns The user_id string if it exists, otherwise null.
 */
export function getUserId(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(USER_ID);
  }
  return null;
}

/**
 * Retrieves the token from local storage.
 * @returns The token string if it exists, otherwise null.
 */
export function getToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(TOKEN_KEY);
  }
  return null;
}

export function getBearerToken(): string | null {
  if (typeof window !== 'undefined') {
    return `Bearer ${localStorage.getItem(TOKEN_KEY)}`;
  }
  return null;
}

/**
 * Deletes the token from local storage.
 */
export function deleteToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TOKEN_KEY);
  }
}

/**
 * Decodes a given JWT token.
 * @param token - The JWT token string to decode.
 * @returns The decoded token payload or null if decoding fails.
 */
export function decodeToken<T = any>(token: string): T | null {
  try {
    return jwt_decode.jwtDecode<T>(token);
  } catch (error) {
    console.error("Error decoding token:", error);
    return null;
  }
}

/**
 * Retrieves the token from local storage and returns its decoded value.
 * @returns The decoded token payload or null if token doesn't exist or decoding fails.
 */
export function getDecodedToken<T = any>(): T | null {
  const token = getToken();
  if (token) {
    return decodeToken<T>(token);
  }
  return null;
}
