import { getUserId } from "@/app/tokenStorage";
import { EmailVisibility } from "../../constants";
import { expandMapsUrl } from "./expandMapsUrl";

export function obscureEmail(email: string | undefined, level: EmailVisibility | undefined): string {
  if (!(email && level)) return ""
  if (level === "NONE") return "";
  if (level === "FULL") return email;

  // PARTIAL: fixed obfuscation like a***@domain.com
  const [localPart, domain] = email.split("@");
  if (!localPart || !domain) return email; // fallback for invalid format

  const firstChar = localPart.charAt(0);
  return `${firstChar}***@${domain}`;
}

export const trimmed = (v?: string) => v?.trim() || "";

export function capitalizeWords(str: string): string {
  if (!str) return "";
  return str
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);


export const generateVCF = async (contactInfo: any) => {
  const lines: string[] = [
    "BEGIN:VCARD",
    "VERSION:3.0",
  ];

  if (contactInfo?.lastName || contactInfo?.firstName)
    lines.push(`N:${contactInfo?.lastName || ""};${contactInfo?.firstName || ""};;;`);

  if (contactInfo?.firstName || contactInfo?.lastName)
    lines.push(`FN:${[contactInfo?.firstName, contactInfo?.lastName].filter(Boolean).join(" ")}`);

  if (contactInfo?.company) lines.push(`ORG:${contactInfo.company}`);
  if (contactInfo?.role) lines.push(`TITLE:${contactInfo.role}`);
  if (contactInfo?.phoneNumber) lines.push(`TEL:${contactInfo.phoneNumber.replaceAll(" ", "")}`);
  if (contactInfo?.emailAddress) lines.push(`EMAIL:${contactInfo.emailAddress}`);
  if (contactInfo?.location) {
    const adrField = await getAdrFieldFromSharedMapsUrl(contactInfo.location);
    if (adrField) lines.push(adrField);
  }
  if (contactInfo?.profileImage) {
    if (contactInfo.profileImage.startsWith("data:image/")) {
      const [meta, base64] = contactInfo.profileImage.split(",");
      const isJPEG = meta.includes("jpeg") || meta.includes("jpg");
      const isPNG = meta.includes("png");
      const imageType = isJPEG ? "JPEG" : isPNG ? "PNG" : "JPEG"; // fallback to JPEG

      // vCard requires line folding: max 75 chars per line, subsequent lines start with space
      const foldedBase64 = base64.match(/.{1,75}/g)?.map((line: any, index: any) => (index === 0 ? line : ` ${line}`)).join("\n");

      lines.push(`PHOTO;ENCODING=b;TYPE=${imageType}:${foldedBase64}`);
    } else {
      // fallback to assuming it's a URL
      lines.push(`PHOTO;VALUE=URL:${contactInfo.profileImage}`);
    }
  }
  if (contactInfo?.website) {
    lines.push(`item1.URL:${contactInfo.website}`);
    lines.push(`item1.X-ABLabel:Homepage`);

    const links = contactInfo?.links || {};
    let itemIndex = 2;

    for (const [platform, url] of Object.entries(links)) {
      if (url) {
        lines.push(`item${itemIndex}.URL:${url}`);
        lines.push(`item${itemIndex}.X-ABLabel:${capitalize(platform)}`);
        itemIndex++;
      }
    }

    // Add links to user profile pages if userId is available
    if (contactInfo?.userId) {
      const baseUrl = "https://beyond-delta.vercel.app";
      lines.push(`item${itemIndex}.URL:${baseUrl}/u/contact/${contactInfo.userId}`);
      lines.push(`item${itemIndex}.X-ABLabel:BeyondCard Profile`);
      itemIndex++;

      if (contactInfo?.hasSocialProof) {
        lines.push(`item${itemIndex}.URL:${baseUrl}/u/social-proof/${contactInfo.userId}`);
        lines.push(`item${itemIndex}.X-ABLabel:Social Proof`);
        itemIndex++;
      }

      if (contactInfo?.hasTestimonial) {
        lines.push(`item${itemIndex}.URL:${baseUrl}/u/testimonials/${contactInfo.userId}`);
        lines.push(`item${itemIndex}.X-ABLabel:Testimonials`);
        itemIndex++;
      }
    }
  }

  lines.push("END:VCARD");

  const vcfContent = lines.join("\n");

  const blob = new Blob([vcfContent], { type: "text/vcard" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${contactInfo.firstName || "contact"}.vcf`;
  a.click();
  URL.revokeObjectURL(url);
};


export const generateVCFInternal = async (contactInfo: any) => {
  const lines: string[] = [
    "BEGIN:VCARD",
    "VERSION:3.0",
  ];

  if (contactInfo?.lastName || contactInfo?.firstName)
    lines.push(`N:${contactInfo?.lastName || ""};${contactInfo?.firstName || ""};;;`);

  if (contactInfo?.firstName || contactInfo?.lastName)
    lines.push(`FN:${[contactInfo?.firstName, contactInfo?.lastName].filter(Boolean).join(" ")}`);

  if (contactInfo?.company) lines.push(`ORG:${contactInfo.company}`);
  if (contactInfo?.jobTitle) lines.push(`TITLE:${contactInfo.jobTitle}`);
  if (contactInfo?.introductoryMessage) lines.push(`NOTE:${contactInfo.introductoryMessage}`);
  if (contactInfo?.phoneNumber) lines.push(`TEL:${contactInfo.phoneNumber.replaceAll(" ", "")}`);
  if (contactInfo?.emailAddress) lines.push(`EMAIL:${contactInfo.emailAddress}`);
  if (contactInfo?.location) {
    const adrField = await getAdrFieldFromSharedMapsUrl(contactInfo.location);
    if (adrField) lines.push(adrField);
  }
  if (contactInfo?.profileImage) {
    if (contactInfo.profileImage.startsWith("data:image/")) {
      const [meta, base64] = contactInfo.profileImage.split(",");
      const isJPEG = meta.includes("jpeg") || meta.includes("jpg");
      const isPNG = meta.includes("png");
      const imageType = isJPEG ? "JPEG" : isPNG ? "PNG" : "JPEG"; // fallback to JPEG

      // vCard requires line folding: max 75 chars per line, subsequent lines start with space
      const foldedBase64 = base64.match(/.{1,75}/g)?.map((line: any, index: any) => (index === 0 ? line : ` ${line}`)).join("\n");

      lines.push(`PHOTO;ENCODING=b;TYPE=${imageType}:${foldedBase64}`);
    } else {
      // fallback to assuming it's a URL
      lines.push(`PHOTO;VALUE=URL:${contactInfo.profileImage}`);
    }
  }
  if (contactInfo?.website) {
    lines.push(`item1.URL:${contactInfo.website}`);
    lines.push(`item1.X-ABLabel:Homepage`);
  }

  const links = contactInfo?.links || {};
  let itemIndex = 2;

  for (const [platform, url] of Object.entries(links)) {
    if (url) {
      lines.push(`item${itemIndex}.URL:${url}`);
      lines.push(`item${itemIndex}.X-ABLabel:${capitalize(platform)}`);
      itemIndex++;
    }
  }

  // Add links to user profile pages if userId is available
  if (contactInfo?.userId) {
    const baseUrl = "https://beyond-delta.vercel.app";
    lines.push(`item${itemIndex}.URL:${baseUrl}/u/contact/${contactInfo.userId}`);
    lines.push(`item${itemIndex}.X-ABLabel:BeyondCard Profile`);
    itemIndex++;

    if (contactInfo?.hasSocialProof) {
      lines.push(`item${itemIndex}.URL:${baseUrl}/u/social-proof/${contactInfo.userId}`);
      lines.push(`item${itemIndex}.X-ABLabel:Social Proof`);
      itemIndex++;
    }

    if (contactInfo?.hasTestimonial) {
      lines.push(`item${itemIndex}.URL:${baseUrl}/u/testimonials/${contactInfo.userId}`);
      lines.push(`item${itemIndex}.X-ABLabel:Testimonials`);
      itemIndex++;
    }
  }

  lines.push("END:VCARD");

  const vcfContent = lines.join("\n");

  const blob = new Blob([vcfContent], { type: "text/vcard" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${contactInfo.firstName || "contact"}.vcf`;
  a.click();
  URL.revokeObjectURL(url);
};

export async function blobUrlToBase64(blobUrl: string) {
  // 1️⃣ Fetch the blob bytes back out of the object-URL
  const blob = await fetch(blobUrl).then((r) => r.blob());

  // 2️⃣ Read the blob as a data-URL using FileReader
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result); // reader.result is a base-64 data URL
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

export async function transformCardData(input: Record<string, any>) {
  // If logoImageUrl is already a base64 string, use it directly
  // Otherwise, try to convert from blob URL (fallback for backward compatibility)
  let logo = null;

  if (input.logoImageUrl) {
    if (typeof input.logoImageUrl === 'string') {
      if (input.logoImageUrl.startsWith('data:image/')) {
        // It's already a base64 string
        logo = input.logoImageUrl;
      } else if (input.logoImageUrl.startsWith('blob:')) {
        // It's a blob URL, try to convert it
        try {
          logo = await blobUrlToBase64(input.logoImageUrl);
        } catch (error) {
          console.error('Failed to convert logo from blob URL:', error);
        }
      }
    }
  }

  return {
    userId: Number(getUserId()),
    cardTemplateId: Number(input.cardType),
    firstName: input.firstName,
    lastName: input.lastName,
    colour: `${input.colour}`,
    logo: logo,
    logoWidth: `${input.logoWidth}`,
    logoHeight: `${input.logoHeight}`,
    role: input.jobTitle,
    completed: true
  };
}

/**
 * Converts a standard Google Maps URL to an embed-ready URL (no API key needed).
 */
export function getEmbedUrlFromGoogleMapsUrl(url: string): string {
  try {
    const decoded = decodeURIComponent(url);

    // Try @lat,lng pattern
    const coordMatch = decoded.match(/@(-?\d+\.\d+),(-?\d+\.\d+),/);
    if (coordMatch) {
      const lat = coordMatch[1];
      const lng = coordMatch[2];
      return `https://www.google.com/maps?q=${lat},${lng}&output=embed`;
    }

    // Try ?query=lat,lng pattern
    const queryMatch = decoded.match(/query=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (queryMatch) {
      const lat = queryMatch[1];
      const lng = queryMatch[2];
      return `https://www.google.com/maps?q=${lat},${lng}&output=embed`;
    }

    // Try /place/<name>
    const placeMatch = decoded.match(/\/place\/([^/]+)/);
    if (placeMatch) {
      const place = placeMatch[1].replace(/\+/g, " ");
      return `https://www.google.com/maps?q=${encodeURIComponent(place)}&output=embed`;
    }

    return url;
  } catch {
    return url;
  }
}



export async function getAdrFieldFromSharedMapsUrl(sharedUrl: string): Promise<string | null> {
  try {
    const expandedUrl = await expandMapsUrl(sharedUrl);
    if (!expandedUrl) return null;

    const decoded = decodeURIComponent(expandedUrl);

    // Step 1: Try to extract lat/lng from @lat,lng
    let lat: string | null = null;
    let lng: string | null = null;

    const atMatch = decoded.match(/@(-?\d+\.\d+),(-?\d+\.\d+),/);
    if (atMatch) {
      lat = atMatch[1];
      lng = atMatch[2];
    }

    const queryMatch = decoded.match(/query=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (!lat && queryMatch) {
      lat = queryMatch[1];
      lng = queryMatch[2];
    }

    // Step 2: If lat/lng found, use reverse geocoding via Nominatim
    if (lat && lng) {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
      );

      const geoData = await response.json();
      const addr = geoData.address || {};

      const street = addr.road || addr.pedestrian || addr.footway || "";
      const city = addr.city || addr.town || addr.village || "";
      const region = addr.state || "";
      const postalCode = addr.postcode || "";
      const country = addr.country || "";

      return `ADR:;;${street};${city};${region};${postalCode};${country}`;
    }

    // Step 3: Fall back to /place/... parsing if no coordinates
    const placeMatch = decoded.match(/\/place\/([^/]+)/);
    if (placeMatch) {
      const parts = placeMatch[1].replace(/\+/g, " ").split(",").map(s => s.trim());
      const [street = "", city = "", region = "", postal = "", country = ""] = parts;
      return `ADR:;;${street};${city};${region};${postal};${country}`;
    }

    return null;
  } catch (err) {
    console.error("Error building ADR from shared maps URL:", err);
    return null;
  }
}

export async function getAdrDetailsFromSharedMapsUrl(sharedUrl: string) {
  try {
    const expandedUrl = await expandMapsUrl(sharedUrl);
    if (!expandedUrl) return null;

    const decoded = decodeURIComponent(expandedUrl);

    // Step 1: Try to extract lat/lng from @lat,lng
    let lat: string | null = null;
    let lng: string | null = null;

    const atMatch = decoded.match(/@(-?\d+\.\d+),(-?\d+\.\d+),/);
    if (atMatch) {
      lat = atMatch[1];
      lng = atMatch[2];
    }

    const queryMatch = decoded.match(/query=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (!lat && queryMatch) {
      lat = queryMatch[1];
      lng = queryMatch[2];
    }

    // Step 2: If lat/lng found, use reverse geocoding via Nominatim
    if (lat && lng) {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
      );

      const geoData = await response.json();
      const addr = geoData.address || {};

      const street = addr.road || addr.pedestrian || addr.footway || "";
      const city = addr.city || addr.town || addr.village || "";
      const region = addr.state || "";
      const postalCode = addr.postcode || "";
      const country = addr.country || "";

      return { street, city, region, country }
    }

    // Step 3: Fall back to /place/... parsing if no coordinates
    const placeMatch = decoded.match(/\/place\/([^/]+)/);
    if (placeMatch) {
      const parts = placeMatch[1].replace(/\+/g, " ").split(",").map(s => s.trim());
      const [street = "", city = "", region = "", postal = "", country = ""] = parts;
      return { street, city, region, country }
    }

    return null;
  } catch (err) {
    console.error("Error building ADR from shared maps URL:", err);
    return null;
  }
}


export type Address = {
  street?: string;
  city?: string;
  region?: string;
  postalCode?: string;
  country?: string;
};

export function formatAddress(address: Address): string {
  const { street, city, region, postalCode, country } = address;

  // Build parts array by filtering out falsy (undefined, empty string) values
  const parts = [street, city, region, postalCode, country].filter(Boolean);

  return parts.join(", ");
}
