"use client";

import { fetchMetaData } from "@/utils/fetchMetaData";
import { useRouter } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { MdPhoto } from "react-icons/md";
import { SocialProof } from "../../interfaces";

export const SocialProofCard: React.FC<{
    proof: SocialProof, onEditPress?: () => void, onRemovePress?: () => void, viewMode?: boolean
}> = ({ proof, onEditPress, onRemovePress, viewMode }) => {
    const router = useRouter();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [imageLoading, setImageLoading] = useState(false);
    const [title, setTitle] = useState();
    const [imageUrl, setImageUrl] = useState();
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Close dropdown if clicked outside
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, []);

    const getLinkMetaData = async () => {
        setImageLoading(true)
        const { title, ogImage } = await fetchMetaData(proof.links)
        // alert(JSON.stringify({ title, ogImage }))
        if (ogImage) {
            setImageUrl(ogImage)
        }
        if (title) {
            setTitle(title)
        }
        setImageLoading(false)
        console.log("Image url: ", ogImage)
    }

    useEffect(() => {
        getLinkMetaData();
    }, []);

    return (
        <div className="relative flex flex-col shadow bg-white border-[1px] border-outline rounded-[0.75rem] pb-[0.87rem]">
            <div
                onClick={() => { window.location.href = proof.links; }}
                className="cursor-pointer"
            >
                <div className="h-[11.06rem] w-full">
                    {
                        imageLoading ?
                            <div className="relative overflow-hidden bg-subtext/50 text-[3.5rem] text-placeholder flex items-center justify-center h-full w-full rounded-t-[0.75rem] before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:animate-[shimmer_1.5s_infinite] before:w-full before:h-full">
                                <MdPhoto />
                            </div>
                            :
                            imageUrl ?
                                <img
                                    src={imageUrl}
                                    alt={title || proof.title}
                                    className="h-full w-full object-contain rounded-t-[0.75rem]"
                                /> :
                                <div className="bg-outline text-[3.5rem] text-placeholder flex items-center justify-center h-full w-full rounded-t-[0.75rem]">
                                    <MdPhoto />
                                </div>
                    }
                </div>

                <div className="flex gap-[1rem] w-full justify-between items-start">
                    <div className={`bg-white pt-[0.57rem] min-w-0 ${viewMode ? 'mx-[0.94rem]' : 'ml-[0.94rem]'} flex-1`}>
                        <div className="text-[0.9375rem] w-full truncate">{proof.title || title}</div>
                        <div className="text-[0.8125rem] w-full truncate text-placeholder">{proof.links}</div>
                    </div>

                    {
                        !viewMode &&
                        <div
                            ref={dropdownRef}
                            className="relative cursor-pointer mt-[1rem]"
                            onClick={(e) => {
                                e.stopPropagation();
                                setIsDropdownOpen((prev) => !prev);
                            }}
                        >
                            <img
                                className="w-[0.94rem] h-[0.94rem] mr-[1.19rem]"
                                src="/ellipsis.svg"
                            />
                            {isDropdownOpen && (
                                <div
                                    className="absolute border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem] bg-white rounded-md z-[2]"
                                >
                                    <div
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onEditPress?.();
                                        }}
                                        className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext"
                                    >
                                        Edit
                                    </div>
                                    <div
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onRemovePress?.();
                                        }}
                                        className="text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger"
                                    >
                                        Remove link
                                    </div>
                                </div>
                            )}
                        </div>
                    }
                </div>
            </div>
        </div>
    );
};
