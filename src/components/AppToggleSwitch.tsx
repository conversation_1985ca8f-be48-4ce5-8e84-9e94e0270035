import React from 'react'

interface Props {
    widthInRem: number;
    heightInRem: number;
    on: boolean;
    onToggle: (value: boolean) => void
}

export default function AppToggleSwitch({ widthInRem, heightInRem, on, onToggle }: Props) {
    const innerHeight = (heightInRem - 0.38)
    return (
        <div
            style={{
                width: `${widthInRem}rem`,
                height: `${heightInRem}rem`,
            }}
            onClick={() => onToggle(!on)}
            className={`rounded-full ${on ? 'bg-green' : 'bg-outline'} duration-300 flex items-center px-[0.19rem]`}
        >
            <div
                style={{
                    width: `${innerHeight}rem`,
                    height: `${innerHeight}rem`,
                    transform: `translateX(${on ? widthInRem - 0.38 - innerHeight : 0}rem)`,
                }}
                className="transition-transform duration-300 bg-white rounded-full"
            />
        </div>

    )
}
