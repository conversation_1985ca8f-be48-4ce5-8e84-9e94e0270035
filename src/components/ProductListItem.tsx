import React, { useState } from "react";
import { Cg<PERSON>heck } from "react-icons/cg";
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '@/store/store';
import { addItem, decrementItem, setItemQuantity, removeItem, clearCart } from '@/store/features/cartSlice';

interface ProductListItemProps {
  id: number | string;
  imageSrc: string; // Source URL of the product image
  title: string; // Title of the product
  price: number; // Price of the product
  oldPrice?: number;
}

const ProductListItem: React.FC<ProductListItemProps> = ({ id, imageSrc, title, price, oldPrice }) => {
  const cart = useSelector((state: RootState) => state.cart);
  const dispatch = useDispatch();

  return (
    <div className="flex flex-col items-center">
      <img className="w-[10.94rem] h-[11.56rem] rounded-[0.4375rem]" src={imageSrc} alt={title} />
      <div className="font-medium text-center mt-[0.94rem]">
        <div>{title}</div>
        <div className="flex gap-1 justify-center items-center">
          <div className="flex gap-[0.31rem] items-start">
            <div className="text-green">${price}</div>
            {oldPrice && <div className="text-placeholder mt-[0.1rem] font-medium text-[0.75rem] line-through">${oldPrice}</div>}
          </div>
          {
            !cart.items.some((item) => item.id == id) ?
              <div onClick={() => { dispatch(addItem({ id, name: title, price })) }} className="flex justify-center items-center bg-placeholder w-5 h-5 text-white border border-outline font-semibold rounded-full">+</div>
              :
              <div onClick={() => { dispatch(removeItem(id)) }} className="flex justify-center items-center bg-green w-5 h-5 text-white border border-outline font-semibold rounded-full"><CgCheck size={40} /></div>
          }
        </div>
      </div>
    </div>
  );
};

export default ProductListItem;
