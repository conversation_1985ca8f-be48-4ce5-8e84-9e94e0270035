import React from "react";

type AppButtonProps = {
  widthClass: string;
  heightClass?: string;
  fontSizeClass?: string;
  text: string; // The text to display on the button
  alt?: boolean; // Determines the button style
  disabled?: boolean; // Determines the button disabled state
  loading?: boolean;
  onClick?: () => void;
};

const AppButton: React.FC<AppButtonProps> = ({
  widthClass,
  heightClass = "h-[3.13rem]",
  fontSizeClass,
  text,
  alt,
  disabled,
  loading,
  onClick,
}) => {
  return (
    <div
      onClick={(disabled || loading) ? () => { } : onClick}
      className={`flex cursor-pointer font-medium border-[0.055rem] items-center justify-center rounded-[0.55125rem] ${widthClass} ${heightClass} ${fontSizeClass} ${alt ? ("bg-white text-black border-black") : (disabled ? "bg-outline border-outline text-white" : "bg-black text-white border-black")
        }`}
    >
      {!loading ? text :
        <div className="flex items-center justify-center space-x-2">
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce [animation-delay:-0.3s]`}></div>
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce [animation-delay:-0.15s]`}></div>
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce`}></div>
        </div>
      }
    </div>
  );
};

export default AppButton;