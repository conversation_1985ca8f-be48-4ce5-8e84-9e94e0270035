"use client";

import React, { useState, useEffect } from "react";
import { BiChevronDown, BiX } from "react-icons/bi";
import TagComponent from "./TagComponent";
import { TagValue } from "../../constants";
import { capitalizeWords } from "@/utils/helpers";


interface TagSelectInputProps {
  label?: string;
  labelExtra?: string;
  options: TagValue[];
  maxSelectable: number;
  value?: TagValue[]; // ← added prop for initial selected values
  onChange?: (selected: TagValue[]) => void;
}

const TagSelectInput: React.FC<TagSelectInputProps> = ({
  label,
  labelExtra,
  options,
  maxSelectable,
  value = [], // default to empty array
  onChange,
}) => {
  const [selectedTags, setSelectedTags] = useState<TagValue[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Populate selected tags on mount or when value changes
  useEffect(() => {
    if (value.length > 0) {
      const initial = options.filter((tag) =>
        value.map((item)=>item.toLowerCase()).includes(tag)
      );
      setSelectedTags(initial);
    }
  }, [value, options]);



  const availableOptions = options.filter(
    (opt) => !selectedTags.find((t) => t.toLowerCase() === opt.toLowerCase())
  );

  const handleSelect = (tag: TagValue) => {
    if (selectedTags.length >= maxSelectable) return;

    const updated = [...selectedTags, tag];
    setSelectedTags(updated);
    onChange?.(updated);
  };

  const handleRemove = (tagValue: TagValue) => {
    const updated = selectedTags.filter((tag) => tag.toLowerCase() !== tagValue.toLowerCase());
    setSelectedTags(updated);
    onChange?.(updated);
  };

  return (
    <div className="flex flex-col gap-1 relative">
      {label && (
        <label className="font-medium text-[0.9375rem] text-headertext">
          {label}{" "}
          <span className="text-placeholder">
            {labelExtra && `(${labelExtra})`}
          </span>
        </label>
      )}
      <div
        className="relative bg-inputbg border border-outline rounded-[0.625rem] cursor-pointer"
        onClick={() => {
          if (selectedTags.length < maxSelectable) {
            setDropdownOpen((prev) => !prev);
          }
        }}

      >
        <div className="flex items-center flex-wrap h-[2.2rem] gap-2 px-2">
          {selectedTags.map((tag, index) => (
            <TagComponent key={index} name={tag} handleXClick={handleRemove} />
          ))}
        </div>
        <BiChevronDown className="absolute text-[1.4rem] right-2 top-1/2 -translate-y-1/2 text-placeholder" />
        {dropdownOpen && availableOptions.length > 0 && (
          <div className="absolute z-[2] bottom-0 translate-y-full flex flex-wrap gap-[0.5rem] bg-white border border-gray-300 rounded mt-1 p-2 w-full shadow">
            {availableOptions.map((tag, index) => (
              <TagComponent key={index} handleSelect={handleSelect} name={tag} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TagSelectInput;
