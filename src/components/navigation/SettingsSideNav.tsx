"use client";

import { useRouter, usePathname } from "next/navigation";
import { useMediaQuery } from "usehooks-ts";
import ContactNav from "../ContactNav";
import AccountSettingsContext from "@/contexts/AccountSettingsContext";
import { useState } from "react";
import GoBackButton from "../GoBackButton";

export default function SettingsSideNav({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const router = useRouter();
    const pathname = usePathname();
    const [showOrderSettingsMobile, setShowOrderSettingsMobile] = useState(false);

    const navItems = [
        { label: "Orders", route: "/account-settings/orders" },
        { label: "Change password", route: "/account-settings/change-password" },
        { label: "Account status", route: "/account-settings/profile-status" },
        { label: "Support", route: "/account-settings/support" },
    ];

    const isMobile = useMediaQuery('(max-width: 1023px)');
    const currentNavItem = navItems.find((item) => (item.route == pathname))

    return (
        <div className="flex flex-col flex-1">
            <div className="md:ml-[6rem] xl:ml-[17.63rem] xl:mt-[7.81rem] mt-[1.38rem] mx-[0.94rem]">
                <div className={`lg:hidden ${(!showOrderSettingsMobile && currentNavItem?.label == "Orders") ? 'hidden' : ''}`}><GoBackButton onClickOverride={() => { if (showOrderSettingsMobile) { setShowOrderSettingsMobile(false) } else { router.back() } }} /></div>
                {(!isMobile || currentNavItem?.label == "Orders") &&
                    <div className="font-medium md:font-semibold mt-[0.94rem] text-[1.125rem]  md:text-[1.25rem] text-headertext">{isMobile ? (currentNavItem?.label == "Orders" ? (showOrderSettingsMobile ? "Orders" : "Settings") : currentNavItem?.label) : "Settings"}</div>
                }
                <div className="mt-[0.63rem] flex-1 md:mt-[1.88rem] flex">
                    <div className={`${(isMobile && (showOrderSettingsMobile || currentNavItem?.label != "Orders")) ? "hidden" : ""} flex flex-col -md:w-full gap-[0.63rem] md:gap-[1.5rem]`}>
                        {navItems.map((item, index) => (
                            <>
                                <div
                                    key={index}
                                    onClick={() => router.push(item.route)}
                                    className={`-md:hidden text-[0.75rem] cursor-pointer ${pathname === item.route ? "font-semibold" : "text-subtext"
                                        }`}
                                >
                                    {item.label}
                                </div>
                                {
                                    item.label == "Orders" ?
                                        <div className="md:hidden">

                                            <ContactNav
                                                onClick={() => { router.push(item.route); setShowOrderSettingsMobile(true) }}
                                                text={item.label}
                                            />
                                        </div>
                                        :
                                        <div className="md:hidden">
                                            <ContactNav
                                                onClick={() => router.push(item.route)}
                                                text={item.label}
                                            />
                                        </div>
                                }
                            </>

                        ))}
                    </div>
                    <AccountSettingsContext value={{ showOrderSettingsMobile, setShowOrderSettingsMobile }}>
                        {children}
                    </AccountSettingsContext>
                </div>
            </div>
        </div>
    );
}
