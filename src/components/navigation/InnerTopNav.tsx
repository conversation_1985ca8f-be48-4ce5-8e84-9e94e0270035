"use client";

import React, { useEffect, useRef, useState } from 'react'
import HNavList from './HNavList';
import { usePathname, useRouter } from 'next/navigation';
import { FaHamburger } from 'react-icons/fa';
import { TbMenu } from 'react-icons/tb';
import { deleteToken } from '@/app/tokenStorage';
import { useAuthStore } from '@/store/authStore';
import { MdAccountCircle } from 'react-icons/md';
import Modal from '../Modal';

function InnerTopNav() {
    const router = useRouter();
    const pathname = usePathname();
    const [selectedPath, setSelectedPath] = useState("/dashboard");
    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { user, logout } = useAuthStore();


    const dropdownRef = useRef<HTMLDivElement>(null);

    // Close dropdown if clicked outside
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
                setIsSettingsDropdownOpen(false);
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, []);

    useEffect(() => {
        setIsSettingsDropdownOpen(false)
    }, [pathname]);

    const navItems = [
        { text: "Dashboard", path: "/dashboard" },
        { text: "Profile", path: "/landing-page" },
        // { text: "Product catalog", path: "/p-catalog" },
        // { text: "Service catalog", path: "/s-catalog" },
        { text: "Testimonials", path: "/testimonials" },
        { text: "Social Proofs", path: "/social-proof" },
        { text: "Contacts", path: "/contacts" },
    ];


    return (
        <div className="flex items-center sticky top-0 z-[3] justify-between bg-white h-[5.56rem] px-[0.63rem] md:px-[3.13rem] border-b border-outline">
            <Modal isOpen={isModalOpen}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="bg-white w-full sm:w-[28.75rem] rounded-[0.625rem]">
                        <div className="flex pl-[0.94rem] border-b border-outline pt-[0.94rem] pr-[0.69rem] pb-[0.69rem] items-center justify-between rounded-t-[0.625rem]">
                            <div className="text-[0.875rem] text-headertext font-medium">
                                Log out
                            </div>
                            <img
                                onClick={() => setIsModalOpen(false)}
                                src="/cross-small.svg"
                                className="h-[0.88rem] w-[0.88rem] cursor-pointer"
                            />
                        </div>

                        <div className="flex flex-col px-[0.94rem] pt-[0.94rem] pb-[1.19rem] gap-[1.25rem]">

                            <div className="text-placeholder text-[1rem]">
                                Are you sure you want to log out?
                            </div>

                            <div className="flex w-full gap-[0.75rem]">

                                <div
                                    onClick={() => {
                                        setIsModalOpen(false);
                                    }}
                                    className="flex flex-1 cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center h-[3.13rem] rounded-[0.625rem]"
                                >
                                    Cancel
                                </div>
                                <div
                                    onClick={(e) => { e.stopPropagation(); router.push('/login'); deleteToken(); logout(); }}
                                    className={`flex flex-1 cursor-pointer font-medium bg-black text-white border-black items-center justify-center h-[3.13rem] rounded-[0.625rem]`}
                                >
                                    Yes
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>
            <img src={"/Logo.svg"} className="h-[1.79rem] w-[7.5rem]" />

            <div className='-md:hidden'><HNavList navItems={navItems} /></div>

            <div ref={dropdownRef} className='flex items-center gap-[0.88rem]'>
                <div
                    onClick={() => { setIsSettingsDropdownOpen((prev) => !prev) }}
                    className="relative cursor-pointer md:hidden"
                >
                    <div className='text-2xl'>
                        <TbMenu />
                    </div>
                    {
                        isSettingsDropdownOpen &&
                        <div className='absolute z-[2] bg-white mt-[0.43rem] border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem]'>
                            {
                                navItems.map((navItem, index) => (
                                    <div key={index} onClick={(e) => { e.stopPropagation(); router.push(navItem.path); }} className='text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext'>
                                        {navItem.text}
                                    </div>
                                ))
                            }
                            <div onClick={(e) => { e.stopPropagation(); router.push('/account-settings/orders'); }} className='text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext'>
                                Account settings
                            </div>
                            <div onClick={() => { setIsModalOpen(true) }} className='text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger'>
                                Log out
                            </div>

                        </div>
                    }
                </div>
                <div
                    onClick={() => { setIsSettingsDropdownOpen((prev) => !prev) }}
                    className="relative cursor-pointer -md:hidden"
                >
                    {
                        user?.profileImage ?
                            <img
                                className="w-[3.13rem] object-cover rounded-full h-[3.13rem]"
                                src={user.profileImage}
                            />
                            :
                            <MdAccountCircle className="w-[3.13rem] text-headertext/90 rounded-full h-[3.13rem]" />
                    }
                    <div className='absolute bottom-0 -right-3 z-[2] p-1 rounded-full bg-white border border-outline shadow'>
                        <img
                            className="w-[1rem] h-[1rem]"
                            src="/settings.svg"
                        />
                    </div>
                    {
                        isSettingsDropdownOpen &&
                        <div className='absolute z-[2] bg-white mt-[0.43rem] border right-0 border-outline shadow w-[9.75rem] px-[0.81rem] pt-[0.88rem] pb-[1rem] flex flex-col gap-[1.38rem]'>
                            <div onClick={(e) => { e.stopPropagation(); router.push('/account-settings/orders'); }} className='text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-subtext'>
                                Account settings
                            </div>
                            <div onClick={() => { setIsModalOpen(true) }} className='text-[0.875rem] cursor-pointer duration-200 hover:font-semibold text-danger'>
                                Log out
                            </div>

                        </div>
                    }
                </div>

            </div>

        </div>
    )
}

export default InnerTopNav