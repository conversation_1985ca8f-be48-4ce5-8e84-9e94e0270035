import { usePathname, useRouter } from "next/navigation";
import React from "react";

interface NavItem {
  text: string;
  path: string;
}

interface HNavListProps {
  navItems: NavItem[];
  // value: string;
  // onChange: (newValue: string) => void;
}

const HNavList: React.FC<HNavListProps> = ({ navItems }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="flex items-center gap-[0.75rem]">
      {navItems.map((item) => (
        <div
          key={item.text}
          onClick={() => { router.push(item.path) }}
          className={`px-[1rem] py-[0.5rem] rounded-full font-semibold text-[0.75rem] cursor-pointer ${pathname === item.path
            ? "bg-grey text-headertext"
            : "text-subtext"
            }`}
        >
          {item.text}
        </div>
      ))}
    </div>
  );
};

export default HNavList;
