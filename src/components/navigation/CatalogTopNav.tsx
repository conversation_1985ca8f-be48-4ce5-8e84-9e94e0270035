"use client";

import React, { useState } from 'react'
import <PERSON><PERSON><PERSON><PERSON>ist from './HNavList';
import { usePathname, useRouter } from 'next/navigation';

function CatalogTopNav() {
    const router = useRouter();
    const pathname = usePathname();

    const navItems = [
        { text: "Dashboard", path: "/p-catalog", tabPaths: ["/p-catalog", "/p-catalog/setup"] },
        { text: "Products", path: "/p-catalog/products", tabPaths: ["/p-catalog/products"] },
        { text: "Orders", path: "/p-catalog/orders", tabPaths: ["/p-catalog/orders"] },
        { text: "Special offers", path: "/p-catalog/special-offers", tabPaths: ["/p-catalog/special-offers"] },
        { text: "Inquiries", path: "/p-catalog/inquiries", tabPaths: ["/p-catalog/inquiries"] },
    ];


    return (
        <div className="flex h-[3.13rem] px-[3.13rem] items-center gap-[2.25rem]">
            {navItems.map((item) => (
                <div
                    key={item.text}
                    onClick={() => { router.push(item.path) }}
                    className={`flex items-center justify-center h-[3.13rem] text-[0.75rem] cursor-pointer ${item.tabPaths?.includes(pathname)
                        ? "font-medium border-b border-black text-headertext"
                        : "text-subtext"
                        }`}
                >
                    {item.text}
                </div>
            ))}
        </div>
    )
}

export default CatalogTopNav