import React from "react";
import { CgArrowTopRight } from "react-icons/cg";

interface ContactNavProps {
  onClick?: () => void; // Function to handle click events (optional for non-clickable items)
  text: string; // Text to display in the navigation item
  hasArrow?: boolean; // Determines if the chevron arrow is displayed, defaults to true
  isExternalLink?: boolean;
}

const ContactNav: React.FC<ContactNavProps> = ({ onClick, text, hasArrow = true, isExternalLink = false }) => {
  return (
    <div
      onClick={onClick}
      className={`flex ${onClick ? "cursor-pointer" : ""} justify-between items-center pl-[0.63rem] pr-[0.88rem] pt-[1.06rem] pb-[0.94rem] bg-white rounded-[0.625rem] shadow-lg`}
    >
      <div className="text-headertext">{text}</div>
      {hasArrow && <img src="/chevron-right-small.svg" className="h-[1rem] w-[1rem]" />}
      {isExternalLink && <CgArrowTopRight className="text-gray-500" />}
    </div>
  );
};

export default ContactNav;
