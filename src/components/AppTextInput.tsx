import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>Eye, AiOutlineEyeInvisible } from 'react-icons/ai'; // Import React Icons

interface InputFieldProps {
    label: string; // Label text
    labelExtra?: string; // Label extra text
    placeholder?: string; // Placeholder for the input field
    value?: string; // Current value of the input field
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void; // Change handler
    type?: string; // Input type, default is "text"
}

const AppTextInput: React.FC<InputFieldProps> = ({
    label,
    labelExtra,
    placeholder = '',
    value,
    onChange,
    type = 'text',
}) => {
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const isPasswordType = type === 'password';

    return (
        <div className="h-[4.69rem] flex flex-col justify-between relative">
            <label className="font-medium text-[0.9375rem] text-headertext">
                {label}{' '}
                <span className="text-placeholder">
                    {labelExtra && `(${labelExtra})`}
                </span>
            </label>
            <div className="relative">
                <input
                    type={isPasswordType && showPassword ? 'text' : type}
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    className="outline-none bg-inputbg placeholder-placeholder border-outline border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] h-[3.13rem] w-full pr-[2.5rem]"
                />
                {isPasswordType && (
                    <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-placeholder focus:outline-none"
                    >
                        {showPassword ? (
                            <AiOutlineEye className="w-5 h-5" />
                        ) : (
                            <img src="/eye-crossed.svg" alt="Hide password" className="w-4 h-4" />
                        )}
                    </button>
                )}
            </div>
        </div>
    );
};

export default AppTextInput;
