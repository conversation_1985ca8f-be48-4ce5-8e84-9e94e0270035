import React from "react";
import {
    Chart as ChartJS,
    LineElement,
    PointElement,
    LinearScale,
    Title,
    Tooltip,
    Filler,
    CategoryScale,
    ChartOptions,
} from "chart.js";
import { Line } from "react-chartjs-2";
import colors from "../../../themePalette";

// Register required Chart.js components
ChartJS.register(LineElement, PointElement, LinearScale, Title, Tooltip, Filler, CategoryScale);

interface ChartData {
    labels: string[];
    datasets: {
        label: string;
        data: number[];
        fill: boolean;
        backgroundColor: string;
        borderColor: string;
        tension: number;
    }[];
}

const LineChart: React.FC = () => {
    const data: ChartData = {
        labels: ["1/12", "2/12", "3/12", "4/12", "5/12", "6/12", "7/12", "8/12", "9/12", "10/12", "11/12", "12/12"],
        datasets: [
            {
                label: "Value",
                data: [0, 2000, 3000, 4000, 6000, 5000, 5500, 3500, 7000, 6500, 6000, 5500],
                fill: true,
                backgroundColor: "rgba(76, 175, 80, 0.2)", // Light green gradient
                borderColor: "#4caf50", // Green border color
                tension: 0.4, // Smooth curve
            },
        ],
    };

    const options: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false, // Allows dynamic resizing
        plugins: {
            legend: { display: false }, // Hide the legend
            tooltip: {}, // Enable tooltips
        },
        scales: {
            x: {
                grid: { display: false }, // Hide X-axis grid lines
                ticks: {
                    color: colors.placeholder,
                    font: { size: 9.22 },
                },
            },
            y: {
                grid: { display: true },
                ticks: {
                    callback: (value: number | string) => {
                        if (typeof value === "number" && value >= 1000) {
                            return `${Math.floor(value / 1000)}K`;
                        }
                        return value.toString();
                    },
                    color: colors.placeholder,
                    font: { size: 9.22 },
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", height: "100%" }}>
            <Line data={data} options={options} />
        </div>
    );
};

export default LineChart;
