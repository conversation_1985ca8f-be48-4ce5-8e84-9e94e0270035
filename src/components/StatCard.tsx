import React from "react";
import { Tooltip } from "react-tooltip";

interface StatCardProps {
    title: string;
    value: number | string;
    percentageChange: string;
    periodDescription: string;
    ttId: string;
    ttText: string;
    ttTextWidthClass: string;
}

const StatCard: React.FC<StatCardProps> = ({
    title,
    value,
    percentageChange,
    periodDescription,
    ttId,
    ttText,
    ttTextWidthClass
}) => {
    return (
        <div className="flex flex-col justify-end rounded-[0.9375rem] border border-outline px-[0.75rem] sm:px-[1.31rem] pt-[0.875rem] pb-[0.675rem] sm:pt-[1.13rem] sm:pb-[1.31rem]">
            <div className="flex items-center gap-[0.44rem]">
                <div className="font-semibold text-[0.875rem] sm:text-[1rem] text-subheading">{title}</div>
                <img data-tooltip-id={ttId} className="w-[0.88rem] h-[0.88rem]" src="/info-green.svg" alt="Info icon" />
                <Tooltip style={{ background: "white", boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }} id={ttId}>
                    <div className={`${ttTextWidthClass} font-semibold text-[0.75rem] text-subheading bg-white`}>
                        {ttText}
                    </div>
                </Tooltip>
            </div>

            <div className="text-[2.8125rem] sm:text-[6.0625rem] font-semibold">{value}</div>

            <div className="flex items-center gap-1 text-[0.875rem] sm:text-[1rem]">
                <span className="font-semibold text-placeholder">{percentageChange}</span>
                <span className="font-semibold text-subheading">{periodDescription}</span>
            </div>
        </div>
    );
};

export default StatCard;
