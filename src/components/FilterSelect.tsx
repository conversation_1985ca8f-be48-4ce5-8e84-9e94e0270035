import React, { useState } from "react";

interface FilterSelectProps {
  options: string[];
  defaultOption: string;
  onChange?: (value: string) => void;
}

const FilterSelect: React.FC<FilterSelectProps> = ({ options, defaultOption, onChange }) => {
  const [selectedOption, setSelectedOption] = useState(defaultOption);
  const [isOpen, setIsOpen] = useState(false);

  const handleOptionClick = (option: string) => {
    setSelectedOption(option);
    setIsOpen(false);
    if (onChange) onChange(option);
  };

  return (
    <div className="relative">
      {/* Selected option */}
      <div
        className="flex justify-between items-center bg-grey px-[0.88rem] h-[2.25rem] min-w-[8.05rem] rounded-[0.9375rem] border-[0.125rem] border-outline cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="text-headertext text-[0.75rem]">{selectedOption}</div>
        <img className="w-[0.88rem] h-[0.88rem]" src="/angle-down-small.svg" alt="Info" />
      </div>

      {/* Dropdown options */}
      {isOpen && (
        <div className="absolute z-10 mt-1 bg-white shadow-md rounded-[0.9375rem] border border-outline min-w-[8.05rem]">
          {options.map((option) => (
            <div
              key={option}
              className="px-[0.88rem] py-[0.5rem] text-headertext text-[0.75rem] hover:bg-grey cursor-pointer"
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilterSelect;
