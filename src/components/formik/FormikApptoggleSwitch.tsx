"use client";

import React from "react";
import { Field } from "formik";

interface Props {
  name: string; // Formik field name
  widthInRem: number;
  heightInRem: number;
}

export default function FormikAppToggleSwitch({ name, widthInRem, heightInRem }: Props) {
  const innerHeight = heightInRem - 0.38;

  return (
    <Field name={name}>
      {({ field, form }: any) => {
        const on = field.value;
        const handleToggle = () => {
          form.setFieldValue(name, !on);
        };

        return (
          <div
            style={{
              width: `${widthInRem}rem`,
              height: `${heightInRem}rem`,
            }}
            onClick={handleToggle}
            className={`rounded-full ${on ? "bg-green" : "bg-outline"} duration-300 flex items-center px-[0.19rem] cursor-pointer`}
          >
            <div
              style={{
                width: `${innerHeight}rem`,
                height: `${innerHeight}rem`,
                transform: `translateX(${on ? widthInRem - 0.38 - innerHeight : 0}rem)`,
              }}
              className="transition-transform duration-300 bg-white rounded-full"
            />
          </div>
        );
      }}
    </Field>
  );
}
