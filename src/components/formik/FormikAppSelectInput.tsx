"use client";

import React from "react";
import { Field } from "formik";

interface SelectInputProps {
    name: string; // Name for Formik field
    label: string;
    labelExtra?: string;
    options: { value: string; label: string }[];
}

const FormikAppSelectInput: React.FC<SelectInputProps> = ({
    name,
    label,
    labelExtra,
    options,
}) => {
    return (
        <div className="h-[4.69rem] flex flex-col justify-between relative">
            {
                label &&
                <label className="font-medium text-[0.9375rem] text-headertext">
                    {label}{" "}
                    <span className="text-placeholder">{labelExtra && `(${labelExtra})`}</span>
                </label>
            }
            <div className="relative">
                <Field name={name}>
                    {({ field, meta }: any) => (
                        <>
                            <select
                                {...field}
                                className={`outline-none bg-inputbg placeholder-placeholder border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] h-[3.13rem] w-full appearance-none ${meta.touched && meta.error ? "border-red-500" : "border-outline"
                                    }`}
                            >
                                <option value="" disabled>
                                    Select an option
                                </option>
                                {options.map((option, index) => (
                                    <option key={index} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-placeholder">
                                <img src="/angle-small-down-gray.svg" className="w-[0.88rem] h-[0.88rem]" />
                            </div>
                            {meta.touched && meta.error && <p className="text-red-500 text-sm mt-1">{meta.error}</p>}
                        </>
                    )}
                </Field>
            </div>
        </div>
    );
};

export default FormikAppSelectInput;
