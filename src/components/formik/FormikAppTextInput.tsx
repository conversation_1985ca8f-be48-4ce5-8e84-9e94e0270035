"use client";

import React, { useState } from "react";
import { Field, ErrorMessage } from "formik";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";

interface InputFieldProps {
    name: string; // Name for Formik field
    label?: string;
    labelExtra?: string;
    placeholder?: string;
    type?: string;
}

const FormikAppTextInput: React.FC<InputFieldProps> = ({
    name,
    label,
    labelExtra,
    placeholder = "",
    type = "text",
}) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPasswordType = type === "password";

    return (
        <div className="h-[4.69rem] flex flex-col justify-between relative">
        {/* <div className="flex flex-col justify-between relative"> */}
            {
                label &&
                <label className="font-medium text-[0.9375rem] text-headertext">
                    {label}{" "}
                    <span className="text-placeholder">{labelExtra && `(${labelExtra})`}</span>
                </label>
            }
            <Field name={name}>
                {({ field, meta }: any) => (
                    <>
                        <div className="relative">
                            <input
                                {...field}
                                type={isPasswordType && showPassword ? "text" : type}
                                placeholder={placeholder}
                                className={`outline-none bg-inputbg placeholder-placeholder border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] h-[3.13rem] w-full pr-[2.5rem] ${meta.touched && meta.error ? "border-red-500" : "border-outline"
                                    }`}
                            />
                            {isPasswordType && (
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-placeholder focus:outline-none"
                                >
                                    {showPassword ? <AiOutlineEye className="w-5 h-5" /> : <img src="/eye-crossed.svg" alt="Hide password" className="w-4 h-4" />}
                                </button>
                            )}
                        </div>
                        {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
                    </>
                )}
            </Field>
        </div>
    );
};

export default FormikAppTextInput;
