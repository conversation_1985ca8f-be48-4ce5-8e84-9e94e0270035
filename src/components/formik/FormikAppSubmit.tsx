"use client";

import React from "react";
import { useFormikContext } from "formik";

type AppButtonProps = {
  widthClass: string;
  heightClass?: string;
  fontSizeClass?: string;
  text: string;
  alt?: boolean;
  disabled?: boolean;
};

const FormikAppSubmit: React.FC<AppButtonProps> = ({
  widthClass,
  heightClass = "h-[3.13rem]",
  fontSizeClass,
  text,
  alt,
  disabled,
}) => {
  const { handleSubmit, isSubmitting } = useFormikContext();

  return (
    <button
      type="submit"
      onClick={() => handleSubmit()}
      disabled={disabled || isSubmitting}
      className={`flex appearance-none font-medium border-[0.055rem] items-center justify-center rounded-[0.55125rem] ${widthClass} ${heightClass} ${fontSizeClass} ${alt
          ? "bg-white text-black border-black cursor-pointer "
          : disabled || isSubmitting
            ? "bg-placeholder border-placeholder text-white cursor-not-allowed"
            : "bg-black text-white border-black cursor-pointer"
        }`}
    >
      {isSubmitting ? (
        <div className="flex items-center justify-center space-x-2">
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce [animation-delay:-0.3s]`}></div>
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce [animation-delay:-0.15s]`}></div>
          <div className={`w-2 h-2 ${alt ? 'bg-headertext' : 'bg-white'} rounded-full animate-bounce`}></div>
        </div>
      ) : text}
    </button>
  );
};

export default FormikAppSubmit;
