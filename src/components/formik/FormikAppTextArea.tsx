"use client";

import React from "react";
import { Field } from "formik";

interface TextAreaFieldProps {
  name: string; // Name for Formik field
  label?: string;
  labelExtra?: string;
  placeholder?: string;
  rows?: number;
}

const FormikAppTextArea: React.FC<TextAreaFieldProps> = ({
  name,
  label,
  labelExtra,
  placeholder = "",
  rows = 4,
}) => {
  return (
    <div className="h-auto flex flex-col justify-between">
      {
        label &&
        <label className="font-medium text-[0.9375rem] text-headertext">
          {label}{" "}
          <span className="text-placeholder">{labelExtra && `(${labelExtra})`}</span>
        </label>
      }      <Field name={name}>
        {({ field, meta }: any) => (
          <>
            <textarea
              {...field}
              placeholder={placeholder}
              rows={rows}
              className={`outline-none bg-inputbg placeholder-placeholder border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] py-[0.5rem] resize-none ${meta.touched && meta.error ? "border-red-500" : "border-outline"
                }`}
            />
            {meta.touched && meta.error && <p className="text-red-500 text-sm mt-1">{meta.error}</p>}
          </>
        )}
      </Field>
    </div>
  );
};

export default FormikAppTextArea;
