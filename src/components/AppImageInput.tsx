import React from 'react';
import { AiOutlinePlus } from 'react-icons/ai';

interface ImageInputProps {
    label: string; // Label text
    onImageChange?: (e: React.ChangeEvent<HTMLInputElement>) => void; // Image change handler
}

const AppImageInput: React.FC<ImageInputProps> = ({ label, onImageChange }) => {
    return (
        <div className="flex flex-col gap-2">
            <label className="font-medium text-[0.9375rem] text-headertext">{label}</label>
            <div className="flex items-center justify-center border-dashed border-[0.0625rem] border-placeholder rounded-[0.625rem] h-[6.25rem] w-[6.25rem] bg-inputbg cursor-pointer relative">
                <input
                    type="file"
                    accept="image/*"
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    onChange={onImageChange}
                />
                <AiOutlinePlus className="text-placeholder w-[1rem] h-[1rem]" />
            </div>
        </div>
    );
};

export default AppImageInput;
