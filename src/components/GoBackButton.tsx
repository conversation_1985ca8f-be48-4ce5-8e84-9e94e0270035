"use client";

import React from "react";
import { useRouter } from "next/navigation";

const GoBackButton: React.FC<{ onClickOverride?: () => void }> = ({ onClickOverride }) => {
  const router = useRouter();

  return (
    <div
      onClick={() => { if (onClickOverride) { onClickOverride() } else { router.back() } }}
      className="flex justify-center bg-grey items-center w-[2.56rem] h-[2.56rem] md:w-[6.69rem] h-[2.56rem] rounded-full gap-[0.19rem] cursor-pointer"
    >
      <img
        className="w-[1.06rem] h-[1.06rem]"
        src="/arrow-left-small.svg"
        alt="Go back"
      />
      <div className="text-placeholder text-[0.875rem] -md:hidden">Go back</div>
    </div>
  );
};

export default GoBackButton;