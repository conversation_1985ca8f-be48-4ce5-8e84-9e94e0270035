import React from "react";

interface ShortBCardProps {
  firstName?: string; // First name of the person
  lastName?: string; // Last name of the person
  jobTitle?: string; // Optional job title
  logoText?: string; // Optional logo (text)
  logoImageUrl?: string; // Optional logo (image URL)
  selected?: boolean;
  bgColor?: string; // Background color (default is black)
  textColor?: string; // Text color (default is white)
  scale?: number;
  transformOrigin?: string;
  logoHeight?: number; // Logo height percentage
  logoWidth?: number; // Logo width percentage
}

const ShortBCard: React.FC<ShortBCardProps> = ({
  firstName,
  lastName,
  jobTitle,
  logoText,
  logoImageUrl,
  selected = false,
  transformOrigin = "top left",
  bgColor = "black", // Default background color
  textColor = "white", // Default text color
  scale = 1,
  logoHeight = 100, // Default to 100%
  logoWidth = 100, // Default to 100%
}) => {
  return (
    <div
      className="w-[17.5rem] h-[10.51rem] shadow-lg rounded-[1rem]"
      style={{
        backgroundColor: bgColor,
        color: textColor,
        transformOrigin: transformOrigin,
        transform: `scale(${scale})`,
      }}
    >
      {/* First layout: First and Last Name */}
      {!jobTitle && !(logoText || logoImageUrl) && (
        <div className="pt-[3.68rem] whitespace-nowrap pl-[1.48rem] w-[9.81rem] text-[1.5rem] font-medium">
          {firstName} <br /> {lastName}
        </div>
      )}

      {/* Second layout: First/Last Name with Job Title */}
      {jobTitle && !(logoText || logoImageUrl) && (
        <div>
          <div className="whitespace-nowrap pt-[3.24rem] pl-[1.1rem] w-[9.81rem] text-[1.5rem] font-medium">
            {firstName} <br /> {lastName}
          </div>
          <div className="whitespace-nowrap text-[0.8125rem] pl-[1.1rem] mt-[0.33rem]">
            {jobTitle}
          </div>
        </div>
      )}

      {/* Third layout: First/Last Name, Job Title, and logo */}
      {jobTitle && (logoText || logoImageUrl) && (
        <div className="flex justify-between">
          <div>
            <div className="whitespace-nowrap pt-[3.24rem] pl-[1.1rem] w-[9.81rem] text-[1.5rem] font-medium">
              {firstName} <br /> {lastName}
            </div>
            <div className="whitespace-nowrap w-[9.81rem] text-[0.8125rem] pl-[1.1rem] mt-[0.33rem]">
              {jobTitle}
            </div>
          </div>
          <div className="pt-[1.33rem] pr-[1.06rem]">
            {logoImageUrl ? (
              <img 
                src={logoImageUrl} 
                alt="Logo" 
                className="object-contain" 
                style={{
                  height: `${logoHeight}%`,
                  width: `${logoWidth}%`,
                  maxHeight: '3rem',
                  transform: `scale(${logoWidth/100}, ${logoHeight/100})`,
                  transformOrigin: 'top right'
                }}
              />
            ) : (
              <div className="text-[1.47rem] whitespace-nowrap font-semibold">{logoText}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ShortBCard;
