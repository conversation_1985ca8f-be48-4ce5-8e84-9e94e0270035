import React, { useState, useEffect } from "react";

interface OTPInputProps {
  numInputs: number; // Number of OTP input fields
  value?: string; // External value to set the OTP input values
  onChange?: (value: string) => void; // Callback to send the OTP value to the parent
}

const OTPInput: React.FC<OTPInputProps> = ({ numInputs, value, onChange }) => {
  const [otp, setOtp] = useState<string[]>(Array(numInputs).fill(""));

  // Update local state when external value changes
  useEffect(() => {
    if (value?.length === numInputs) {
      setOtp(value.split(""));
    }
  }, [value, numInputs]);

  // Handle input change
  const handleChange = (val: string, index: number, target: HTMLInputElement) => {
    if (!/^\d*$/.test(val)) return; // Allow only numeric input

    const updatedOtp = [...otp];
    updatedOtp[index] = val;
    setOtp(updatedOtp);

    // Notify the parent with the updated OTP value
    if (onChange) {
      onChange(updatedOtp.join(""));
    }

    // Move focus to the next input after setting the value
    if (val && index < numInputs - 1) {
      const nextInput = target.nextElementSibling as HTMLInputElement;
      nextInput?.focus();
    }
  };

  // Handle key navigation
  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    const target = e.target as HTMLInputElement;
    if (e.key === "Backspace") {
      if (!target.value && index > 0) {
        const prevInput = target.previousElementSibling as HTMLInputElement;
        prevInput?.focus();
      }
    }
  };

  // Render the input fields
  return (
    <div className="flex gap-[0.5rem] sm:gap-[0.75rem]">
      {otp.map((digit, index) => (
        <input
          key={index}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={digit}
          onChange={(e) => handleChange(e.target.value, index, e.target)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          className="outline-none flex text-center items-center justify-center text-2xl font-medium w-full max-w-[3.75rem] h-[3.13rem] rounded-[0.375rem] border border-outline"
        />
      ))}
    </div>
  );
};

export default OTPInput;
