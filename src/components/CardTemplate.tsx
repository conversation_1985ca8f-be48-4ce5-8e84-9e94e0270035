import React from "react";

interface CardTemplateProps {
    firstName: string; // First name of the person
    lastName: string; // Last name of the person
    jobTitle?: string; // Optional job title
    logoText?: string; // Optional logo (text)
    logoImageUrl?: string; // Optional logo (image URL)
    selected?: boolean;
    bgColor?: string; // Background color (default is black)
    scale?: number;
    transformOrigin?: string;
}

const CardTemplate: React.FC<CardTemplateProps> = ({
    firstName,
    lastName,
    jobTitle,
    logoText,
    logoImageUrl,
    transformOrigin = "top left",
    selected = false,
    bgColor = "black", // Default background color
    scale = 1,
}) => {
    return (
        <div style={{
            transformOrigin: transformOrigin,
            transform: `scale(${scale})`,
        }} className={`px-[0.7rem] py-[1rem] sm:px-[1.34rem] sm:py-[1.75rem] rounded-[0.445rem] border-[0.0444rem] ${selected ? 'border-green' : 'border-outline'}`}>
            <div
                className="w-[19.5rem] h-[10.51rem] shadow-lg rounded-[1rem]"
                style={{ backgroundColor: bgColor }}
            >
                {/* First layout: First and Last Name */}
                {!jobTitle && !(logoText || logoImageUrl) && (
                    <div className="pt-[3.68rem] whitespace-nowrap pl-[1.48rem] w-[9.81rem] text-[1.5rem] font-medium text-white">
                        {firstName} <br /> {lastName}
                    </div>
                )}

                {/* Second layout: First/Last Name with Job Title */}
                {jobTitle && !(logoText || logoImageUrl) && (
                    <div>
                        <div className="whitespace-nowrap pt-[3.24rem] pl-[1.1rem] w-[9.81rem] text-[1.5rem] font-medium text-white">
                            {firstName} <br /> {lastName}
                        </div>
                        <div className="whitespace-nowrap text-[0.8125rem] pl-[1.1rem] text-white mt-[0.33rem]">
                            {jobTitle}
                        </div>
                    </div>
                )}

                {/* Third layout: First/Last Name, Job Title, and logo */}
                {jobTitle && (logoText || logoImageUrl) && (
                    <div className="flex justify-between">
                        <div>
                            <div className="whitespace-nowrap pt-[3.24rem] pl-[1.1rem] w-[9.81rem] text-[1.5rem] font-medium text-white">
                                {firstName} <br /> {lastName}
                            </div>
                            <div className="whitespace-nowrap text-[0.8125rem] pl-[1.1rem] text-white mt-[0.33rem]">
                                {jobTitle}
                            </div>
                        </div>
                        <div className="pt-[1.33rem] pr-[1.06rem]">
                            {logoImageUrl ? (
                                <img src={logoImageUrl} alt="Logo" className="h-[2rem] object-contain" />
                            ) : (
                                <div className="text-[1.47rem] whitespace-nowrap text-white font-semibold">{logoText}</div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CardTemplate;
