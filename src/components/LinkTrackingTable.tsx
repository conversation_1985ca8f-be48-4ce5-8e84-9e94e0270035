import React from "react";
import { Tooltip } from "react-tooltip";



type LinkTrackingTableProps = {
  entries: Record<string, any>;
};

const LinkTrackingTable: React.FC<LinkTrackingTableProps> = ({ entries }) => {
  return (
    <div className="rounded-[0.9375rem] overflow-hidden border border-outline">
      {/* Header */}
      <div className="flex items-center gap-[0.63rem] border-b-[0.03125rem] border-outline pl-[1.31rem] pt-[1.13rem] pb-[1.37rem]">
        <div className="text-subheading font-semibold">Link tracking</div>
        <img data-tooltip-id="lt" className="w-[0.88rem] h-[0.88rem]" src="/info-green.svg" alt="Info" />
        <Tooltip style={{ background: "white", boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }} id="lt">
          <div className="w-[13.75rem] font-semibold text-[0.75rem] text-subheading bg-white">
            This shows the number of times a particular link has been clicked
          </div>
        </Tooltip>
      </div>
      {/* Rows */}
      {Object.entries(entries).reverse().map(([key, value], index) => (
        <div
          key={index}
          className="flex items-center justify-between border-b-[0.03125rem] border-outline pl-[1.31rem] pr-[1.25rem] pt-[0.88rem] pb-[0.94rem]"
        >
          <div className="text-subtext text-[0.9375rem] font-semibold capitalize">{key}</div>
          <div className="text-headertext text-[0.9375rem] font-semibold">{value}</div>
        </div>
      ))}
    </div>
  );
};

export default LinkTrackingTable;
