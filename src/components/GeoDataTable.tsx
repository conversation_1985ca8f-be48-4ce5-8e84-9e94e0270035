import flags from "@/utils/flags_mapping";
import React from "react";
import { Tooltip } from "react-tooltip";

type Row = {
    flag_key: keyof typeof flags;
    label: string;
    value: number | string;
};

type GeoDataTableProps = {
    rows: Row[];
};

const GeoDataTable: React.FC<GeoDataTableProps> = ({ rows }) => {
    return (
        <div className="rounded-[0.9375rem] overflow-hidden border border-outline">
            {/* Header */}
            <div className="flex items-center gap-[0.63rem] border-b-[0.03125rem] border-outline pl-[1.31rem] pt-[1.13rem] pb-[1.37rem]">
                <div className="text-subheading font-semibold">Geographical Data</div>
                <img data-tooltip-id="gd" className="w-[0.88rem] h-[0.88rem]" src="/info-green.svg" alt="Info" />
                <Tooltip style={{ background: "white", boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }} id="gd">
                    <div className="w-[13.75rem] font-semibold text-[0.75rem] text-subheading bg-white">
                    This shows the top 5 locations you links have been opened in ascending order.
                    </div>
                </Tooltip>
            </div>
            <div
                className="flex items-center justify-between border-b-[0.03125rem] border-outline pl-[1.31rem] pr-[1.25rem] pt-[0.88rem] pb-[0.94rem]"
            >
                <div className="text-headertext text-[0.9375rem] font-semibold">Location</div>
                <div className="text-headertext text-[0.9375rem] font-semibold">Last interaction</div>
            </div>
            {/* Rows */}
            {rows.map((row, index) => {
                return (
                    <div
                        key={index}
                        className="flex items-center justify-between pl-[1.31rem] pr-[1.25rem] pt-[0.88rem] pb-[0.94rem]"
                    >
                        <div className="flex items-center gap-[0.44rem]">
                            <div className="w-[1rem] h-[1rem] overflow-hidden border border-outline rounded-full">
                                <img src={flags[row.flag_key]} className="object-cover w-full h-full" alt={row.label} />
                            </div>
                            <div className="text-subtext text-[0.9375rem] font-semibold">{row.label}</div>
                        </div>
                        <div className="text-subtext text-[0.9375rem] font-semibold">{row.value}</div>
                    </div>
                );
            })}
        </div>
    );
};

export default GeoDataTable;
