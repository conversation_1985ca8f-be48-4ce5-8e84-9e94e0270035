"use client";

import client from "@/api/client";
import { useNavigationStore } from "@/store/navigationStore";
import { useMutation } from "@tanstack/react-query";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import OTPInput from "../OTPInput";
import { useNotification } from "@/contexts/NotificationProvider";
import { setToken } from "@/app/tokenStorage";

export default function ResetPassword() {
  // const emailAddress = useSearchParams().get("emailAddress");
  const [errorMessage, setErrorMessage] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [resendAttempts, setResendAttempts] = useState(0);
  const [OTP, setOTP] = useState("");
  const OTPMaxLength = 6;

  const router = useRouter();
  const { data } = useNavigationStore()
  const { showNotification } = useNotification()

  const verify = useMutation({
    mutationFn: async (values: any) => {
      setErrorMessage("")
      return client.post(`/verify_forgot_token`, values);
    },
    onSuccess: (response) => {
      console.log("Verification successful:", response.data);
      const token =
        response.headers["authorization"]
      if (token) {
        console.log(token)
        setToken(token);
        // Navigate to the dashboard
        showNotification({ renderObj: { midSection: "Token verified" }, type: "success" })
        router.push("/create-new-password");
      }
    },
    onError: (error: any) => {
      console.error("Verification failed:", error);
      setErrorMessage(
        error?.response?.data?.message || error?.message ||
        "Verification failed. Please try again."
      );
    },
  });

  const resend = useMutation({
    mutationFn: async (values: any) => {
      return client.post(`/resend_verification_code`, values);
    },
    onSuccess: (response) => {
      console.log("Resend successful:", response.data);
      // Increase the resend attempts and set countdown to a longer duration (e.g. 30s per attempt)
      setResendAttempts((prev) => {
        const newAttempts = prev + 1;
        setCountdown(30 * newAttempts);
        return newAttempts;
      });
    },
    onError: (error: any) => {
      console.error("Resend failed:", error);
      setErrorMessage(
        error?.response?.data?.message || error?.message || "Resend failed. Please try again."
      );
    },
  });

  // Countdown effect: decrease countdown every second
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  return (
    <div className="flex flex-col flex-1 -sm:pb-[10rem] items-center sm:mt-[5.13rem]">
      <div className="flex flex-col -sm:flex-1 -sm:justify-center items-center leading-tighter w-full max-w-[32.31rem] px-[2.69rem] text-center sm:pt-[2.47rem] sm:pb-[2.63rem] bg-white rounded-[0.625rem] sm:shadow-lg">
        <img src={"/envelope.svg"} />
        <div className="text-[1.375rem] font-medium mt-[0.69rem]">
          Check your email
        </div>

        <div className="text-subtext text-center mb-[1.31rem]">
          Enter the code sent to{" "}
          <span className="font-bold text-headertext">{data?.emailAddress}</span>
        </div>

        <OTPInput
          value={OTP}
          onChange={(value) => {
            setOTP(value);
          }}
          numInputs={OTPMaxLength}
        />

        <div className="mt-[0.56rem] text-[0.875rem] font-medium gap-1 flex">
          <div className="text-placeholder">Didn't get code?</div>
          <div
            onClick={() => {
              if (countdown === 0 && !resend.isPending) {
                resend.mutate({ emailAddress: data?.emailAddress });
              }
            }}
            className={`cursor-pointer text-headertext ${countdown > 0 ? "opacity-50" : ""
              }`}
          >
            {resend.isPending
              ? "Sending..."
              : countdown > 0
                ? `Resend in ${countdown}s`
                : "Resend"}
          </div>
        </div>

        {errorMessage && (
          <div className="text-red-500 text-center font-semibold text-[0.9375rem]">
            {errorMessage}
          </div>
        )}

        <div
          onClick={() => {
            if (OTP.length === OTPMaxLength && !verify.isPending) {
              verify.mutate({ emailAddress: data?.emailAddress, forgotPasswordToken: OTP })
            }
          }}
          className={`flex cursor-pointer items-center justify-center w-full max-w-[26.88rem] h-[3.13rem] rounded-[0.625rem] ${OTP.length === OTPMaxLength && !verify.isPending
            ? "bg-black"
            : "bg-placeholder"
            } text-white font-medium my-[1.13rem]`}
        >
          {verify.isPending ? "Verifying..." : "Confirm"}
        </div>

        <div
          onClick={() => {
            router.back();
          }}
          className="flex cursor-pointer justify-center items-center gap-[0.19rem]"
        >
          <img className="w-[1.06rem] h-[1.06rem]" src="/arrow-left-small.svg" />
          <div className="text-placeholder text-[0.875rem]">Go back</div>
        </div>
      </div>
    </div>
  );
}
