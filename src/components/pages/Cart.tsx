"use client";

import AppTextInput from "@/components/AppTextInput";
import BCard from "@/components/BCard";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import GoBackButton from "../GoBackButton";
import ShortBCard from "../ShortBCard";
import { CardDetails, CartItem, useNavigationStore } from "@/store/navigationStore";
import { useAuthStore } from "@/store/authStore";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import { getUserId } from "@/app/tokenStorage";
import { CARD_THEMES } from "../../../constants";
import { transformCardData } from "@/utils/helpers";
import { useNotification } from "@/contexts/NotificationProvider";

export default function Cart() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { data, setData, updateCardDetails } = useNavigationStore();
    const { user, setUser } = useAuthStore();
    const { cardDetails } = data;
    const { showNotification } = useNotification();

    // Initialize cart items with explicit card type handling

    // Debug logging to help identify issues
    useEffect(() => {
        console.log("Cart mounted with data:", data);
        console.log("Card details:", cardDetails);
    }, []);


    const getUserProfile = useMutation({
        mutationFn: async () => {
            return client.post("/profile/portal", { userId: parseInt(getUserId() as string) });
        },
        onSuccess: async (response) => {
            setUser(response.data?.data)
            console.log("Profile retrieval successful:", response.data);

        },
        onError: (error: any) => {
            console.error("Profile retrieval failed:", error);
            router.replace("/login")
        },
    });

    const createCard = useMutation({
        mutationFn: async (values: Record<string, any>) => {
            return client.post("/cards", {...values, });
        },
        onSuccess: async (response) => {
            setData({ ...data, cardDetails: { ...data.cardDetails, id: response.data?.data?.id } })
            router.push("/checkout")

        },
        onError: (error: any) => {
            const msg = error?.response?.data?.message || error?.message || "Card creation failed"
            showNotification({ renderObj: { midSection: msg }, type: "failure" })
            console.error(error);
        },
    });

    useEffect(() => {
        if (!user) {
            getUserProfile.mutate();
        }
    }, [user])

    const updateQuantity = (change: number) => {
       if(data.cardDetails) updateCardDetails({...data.cardDetails, quantity: (data.cardDetails.quantity || 0) + change });
    };

    const selectedColorObj = CARD_THEMES.find((c) => c.id === cardDetails?.colour);

    console.log(selectedColorObj, cardDetails)

    return (
        <div className="flex flex-col flex-1 mt-[1.19rem] px-[1rem] md:px-[2rem] xl:px-[4.38rem]">
            <div>
                <GoBackButton />
            </div>

            <div className="text-[1.375rem] lg:px-[5rem] xl:px-[7rem] font-medium mt-[0.69rem] mb-[1.3rem]">
                Your cart
            </div>

            <div className="flex -lg:flex-col flex-1 gap-[1.06rem] lg:px-[5rem] xl:px-[7rem]">
                <div className="flex-col lg:flex-1 lg:max-w-[35rem] relative overflow-y-auto hide-scrollbar">
                    <div className="w-full">
                        {cardDetails && (() => {
                                // Get color for this specific cart item
                                const itemColorObj = CARD_THEMES.find((c) => c.id === cardDetails?.colour) || CARD_THEMES[0];

                                return (
                                    <div className="relative flex border border-outline rounded-[0.625rem] py-[0.65rem] px-[0.57rem] h-[6.65rem]">
                                        <div className="absolute z-[2]">
                                            {
                                                cardDetails?.cardType === "11" &&
                                                <ShortBCard
                                                    scale={0.5}
                                                    firstName={cardDetails.firstName}
                                                    lastName={cardDetails.lastName}
                                                    bgColor={itemColorObj.hex}
                                                    textColor={itemColorObj.textColorHex}
                                                />
                                            }
                                            {
                                                cardDetails?.cardType === "12" &&
                                                <ShortBCard
                                                    scale={0.5}
                                                    firstName={cardDetails.firstName}
                                                    lastName={cardDetails.lastName}
                                                    jobTitle={cardDetails?.jobTitle}
                                                    bgColor={itemColorObj.hex}
                                                    textColor={itemColorObj.textColorHex}
                                                />
                                            }
                                            {
                                                cardDetails?.cardType === "13" &&
                                                <ShortBCard
                                                    scale={0.5}
                                                    firstName={cardDetails.firstName}
                                                    lastName={cardDetails.lastName}
                                                    jobTitle={cardDetails?.jobTitle}
                                                    logoImageUrl={cardDetails.logoImageUrl}
                                                    logoWidth={cardDetails?.logoWidth}
                                                    logoHeight={cardDetails?.logoHeight}
                                                    bgColor={itemColorObj.hex}
                                                    textColor={itemColorObj.textColorHex}
                                                />
                                            }
                                        </div>

                                        <div className="flex-1 z-[2] flex items-center justify-between pl-[9.32rem]">
                                            <div className="">
                                                {/* <div className="text-[1.125rem] text-headertext font-medium">{item.firstName} {item.lastName}</div> */}
                                                <div className="lg:text-[1.125rem] text-subtext font-medium">GH₵{(cardDetails.price??0).toFixed(2)}</div>
                                            </div>

                                            <div className="flex items-center gap-[1rem]">
                                                <div className="flex items-center">
                                                    <div
                                                        onClick={() => updateQuantity(-1)}
                                                        className="flex cursor-pointer items-center justify-center w-[1.3rem] h-[1.3rem] border border-outline rounded-l-lg"
                                                    >
                                                        -
                                                    </div>
                                                    <div className="flex items-center justify-center w-[1.3rem] h-[1.3rem] bg-grey">
                                                        {cardDetails.quantity}
                                                    </div>
                                                    <div
                                                        onClick={() => updateQuantity(1)}
                                                        className="flex cursor-pointer items-center justify-center w-[1.3rem] h-[1.3rem] border border-outline rounded-r-lg"
                                                    >
                                                        +
                                                    </div>
                                                </div>

                                                <div>
                                                    <img
                                                        src="/trash-red.svg"
                                                        className="cursor-pointer mr-4 w-[1rem] h-[1rem]"
                                                        onClick={() => router.push("/select-card-template")}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })()}
                    </div>
                </div>

                <div className="w-full lg:max-w-[25.56rem]">
                    <div className="bg-grey rounded-[0.625rem] border border-outline pt-[1.13rem] pb-[1.94rem] pl-[1.44rem] pr-[1.31rem]">
                        <div className="flex items-center justify-between text-placeholder font-medium">
                            <div>Subtotal</div>
                            <div>GH₵{((cardDetails?.price ?? 0) * (cardDetails?.quantity ?? 0)).toFixed(2)}</div>
                        </div>
                        <div className="flex items-center mt-[1.06rem] justify-between text-placeholder font-medium">
                            <div>Shipping/Delivery</div>
                            <div><img src="/info-gray.svg" className="w-[0.88rem] h-[0.88rem]" /></div>
                        </div>

                        <div
                            onClick={async () => {
                                try {
                                    // Update cart in navigation store
                                    // Since cardDetails.logoImageUrl is already base64, no need for conversion
                                    const cardData = await transformCardData(cardDetails || {});
                                    createCard.mutate(cardData);
                                } catch (error) {
                                    console.error("Error processing card data:", error);
                                    showNotification({
                                        renderObj: { midSection: "Failed to process card data. Please try again." },
                                        type: "failure"
                                    });
                                }
                            }}
                            className="flex mt-[1.88rem] cursor-pointer items-center justify-center w-full lg:max-w-[22.81rem] h-[3.13rem] rounded-[0.625rem] bg-black text-white font-medium"
                        >
                            Proceed to checkout
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
