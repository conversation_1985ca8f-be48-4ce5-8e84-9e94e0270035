"use client";

import React, { useEffect, useMemo, useState, useRef } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useRouter, useSearchParams } from "next/navigation";

import BCard from "@/components/BCard";
import GoBackButton from "../GoBackButton";
import FormikAppTextInput from "../formik/FormikAppTextInput";
import { CARD_THEMES } from "../../../constants";
import { useNavigationStore } from "@/store/navigationStore";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import { useAuthStore } from "@/store/authStore";
import { getUserId } from "@/app/tokenStorage";
import { useIsFirstRender } from "@/hooks/useIsFirstRender";

export default function CreateCard() {
    const router = useRouter();
    const { data, updateCardDetails } = useNavigationStore();
    const savedCardDetails = data.cardDetails || {};
    // Add this to detect first render (page refresh)
    const [selectedColor, setSelectedColor] = useState<number | null>(
        savedCardDetails.colour !== undefined ? savedCardDetails.colour : 0
    );
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imageFileName, setImageFileName] = useState<string | null>(null);
    const [logoImageUrl, setLogoImageUrl] = useState<string | undefined>(undefined);
    const [logoWidth, setLogoWidth] = useState<number>(
        savedCardDetails.logoWidth || 100
    );
    const [logoHeight, setLogoHeight] = useState<number>(
        savedCardDetails.logoHeight || 100
    );
    const [keepAspectRatio, setKeepAspectRatio] = useState<boolean>(true);
    const [invertLogoColors, setInvertLogoColors] = useState<boolean>(false);
    const originalAspectRatio = useRef<number>(1);
    const originalImageFile = useRef<File | null>(null);
    // Add this to track if we're coming from navigation or direct refresh
    const [isFromNavigation, setIsFromNavigation] = useState<boolean>(false);

    const { user, setUser } = useAuthStore()

    const cardType = useSearchParams().get("cardType");

    const selectedColorObj = CARD_THEMES.find((c) => c.id === selectedColor);
    console.log(logoImageUrl)

    // Add this effect to detect if we're coming from navigation or direct refresh
    useEffect(() => {
        // More reliable way to detect direct page load vs navigation
        const hasNavigationState = Object.keys(savedCardDetails).length > 0;

        if (!hasNavigationState) {
            // This is either a direct page load or the state was cleared
            console.log("Direct page load or cleared state detected");
            setImageFile(null);
            setImageFileName(null)
            setLogoImageUrl(undefined);
            setInvertLogoColors(false);
            originalImageFile.current = null;
            setIsFromNavigation(false);

            updateCardDetails({
                ...savedCardDetails,
                logoImageUrl: "",
                imageFileName: null,
                invertLogoColors: false
            });
        } else {
            // We have navigation state, so we came from another page
            setLogoImageUrl(undefined);
            console.log("Navigation with state detected");
            setIsFromNavigation(true);
        }
    }, [savedCardDetails]);

    // useEffect(() => {
    //     if (!isFromNavigation) return; // Skip if direct refresh

    //     if (savedCardDetails.logoImageUrl) {
    //         // Always set the logo URL first for immediate display
    //         setLogoImageUrl(savedCardDetails.logoImageUrl);

    //         // Only convert from base64 if it's a data URL
    //         if (savedCardDetails.logoImageUrl.startsWith('data:image/')) {
    //             fetch(savedCardDetails.logoImageUrl)
    //                 .then(res => res.blob())
    //                 .then(blob => {
    //                     // Create a file from the blob
    //                     const file = new File([blob], "logo.png", { type: "image/png" });

    //                     // Set the file regardless of invert status
    //                     setImageFile(file);
    //                     setImageFileName(file.name)

    //                     // Handle inverted images
    //                     if (savedCardDetails.invertLogoColors) {
    //                         // Set invert state
    //                         setInvertLogoColors(true);

    //                         // Create original version by inverting again
    //                         // ...existing code for inverting...
    //                     } else {
    //                         originalImageFile.current = file;
    //                         setInvertLogoColors(false);
    //                     }

    //                     // Set other properties
    //                     setLogoWidth(savedCardDetails.logoWidth || 100);
    //                     setLogoHeight(savedCardDetails.logoHeight || 100);
    //                 })
    //                 .catch(err => console.error("Error loading saved logo:", err));
    //         }
    //     }
    // }, [savedCardDetails, isFromNavigation]);

    /* ---------- Yup schema (depends on cardType) ---------- */
    const validationSchema = useMemo(
        () =>
            Yup.object({
                firstName: Yup.string().trim().required("First name is required"),
                lastName: Yup.string().trim().required("Last name is required"),
                jobTitle: cardType === "11"
                    ? Yup.string()
                    : Yup.string().trim().required("Job title is required"),
            }),
        [cardType],
    );

    /* ---------- logo upload helpers (unchanged) ---------- */
    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file && /image\/(png|svg\+xml)/.test(file.type)) {
            // If it's an SVG, convert to PNG first
            if (file.type === "image/svg+xml") {
                const img = new Image();
                img.onload = () => {
                    // Use higher resolution for better quality
                    const canvas = document.createElement('canvas');
                    // Set larger dimensions for better quality
                    const scaleFactor = 8; // 4x or more for better resolution
                    const width = (img.width || 200) * scaleFactor;
                    const height = (img.height || 200) * scaleFactor;

                    canvas.width = width;
                    canvas.height = height;

                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            if (blob) {
                                const pngFile = new File([blob], file.name.replace('.svg', '.png'), { type: 'image/png' });
                                setImageFile(pngFile);
                                setImageFileName(pngFile.name)
                                originalImageFile.current = pngFile;
                                // Create and set the URL for preview
                                setLogoImageUrl(URL.createObjectURL(pngFile));
                                setLogoWidth(100);
                                setLogoHeight(100);
                                setInvertLogoColors(false);
                            }
                        }, 'image/png', 1.0);
                    }
                };
                img.src = URL.createObjectURL(file);
            } else {
                // It's already a PNG
                setImageFile(file);
                setImageFileName(file.name);
                originalImageFile.current = file; // Store original
                // Create and set the URL for preview
                setLogoImageUrl(URL.createObjectURL(file));
                setLogoWidth(100);
                setLogoHeight(100);
                setInvertLogoColors(false);
            }
        }
        else alert("Please upload a PNG or SVG file.");
    };
    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => { e.preventDefault(); handleImageUpload(e as any); };
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => e.preventDefault();

    const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
        const img = e.currentTarget;
        if (img.naturalWidth && img.naturalHeight) {
            originalAspectRatio.current = img.naturalWidth / img.naturalHeight;
        }
    };

    const handleWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newWidth = parseInt(e.target.value);
        setLogoWidth(newWidth);

        if (keepAspectRatio && originalAspectRatio.current) {
            setLogoHeight(newWidth / originalAspectRatio.current);
        }
    };

    const handleHeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newHeight = parseInt(e.target.value);
        setLogoHeight(newHeight);

        if (keepAspectRatio && originalAspectRatio.current) {
            setLogoWidth(newHeight * originalAspectRatio.current);
        }
    };

    const getUserProfile = useMutation({
        mutationFn: async () => {
            return client.post("/profile/portal", { userId: parseInt(getUserId() as string) });
        },
        onSuccess: async (response) => {
            setUser(response.data?.data)
            console.log("Profile retrieval successful:", response.data);

        },
        onError: (error: any) => {
            console.error("Profile retrieval failed:", error);
            router.replace("/login")
        },
    });

    useEffect(() => {
        if (!user) {
            getUserProfile.mutate();
        }
    }, [user])

    // Add this function to handle inverting image colors
    const handleInvertColors = async (invert: boolean) => {
        if (!logoImageUrl) return;

        setInvertLogoColors(invert);

        // If unchecking, revert to original image
        if (!invert && originalImageFile.current) {
            setImageFile(originalImageFile.current);
            setImageFileName(originalImageFile.current.name);
            setLogoImageUrl(URL.createObjectURL(originalImageFile.current));
            return;
        }

        // If inverting but originalFile is missing, fetch it directly from logoImageUrl
        if (invert && !originalImageFile.current) {
            try {
                const response = await fetch(logoImageUrl);
                const blob = await response.blob();
                const fetchedFile = new File([blob], "logo.png", { type: blob.type || 'image/png' });
                originalImageFile.current = fetchedFile;
                setImageFile(fetchedFile);
            } catch (err) {
                console.error("Failed to fetch image from logoImageUrl:", err);
                return;
            }
        }

        const sourceFile = originalImageFile.current;
        if (!sourceFile) {
            console.error("No image file available for inverting.");
            return;
        }

        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error("Failed to get canvas context");
                return;
            }

            const img = new Image();
            img.onload = () => {
                try {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    const data = imageData.data;

                    for (let i = 0; i < data.length; i += 4) {
                        data[i] = 255 - data[i];
                        data[i + 1] = 255 - data[i + 1];
                        data[i + 2] = 255 - data[i + 2];
                    }

                    ctx.putImageData(imageData, 0, 0);

                    canvas.toBlob((blob) => {
                        if (blob) {
                            const newFile = new File([blob], sourceFile.name.replace(/\.(svg|png)$/, '.png'), { type: 'image/png' });
                            setImageFile(newFile);
                            setImageFileName(newFile.name);
                            setLogoImageUrl(URL.createObjectURL(newFile));
                        } else {
                            console.error("Failed to create blob from canvas");
                        }
                    }, 'image/png', 1.0);
                } catch (error) {
                    console.error("Error processing image:", error);
                }
            };

            img.onerror = (error) => {
                console.error("Error loading image:", error);
            };

            img.src = URL.createObjectURL(sourceFile);

        } catch (error) {
            console.error("Error in handleInvertColors:", error);
        }
    };


    /* ---------- render ---------- */
    return (
        <Formik
            initialValues={{
                firstName: savedCardDetails.firstName || user?.firstName || "",
                lastName: savedCardDetails.lastName || user?.lastName || "",
                jobTitle: savedCardDetails.jobTitle || "",
            }}
            validationSchema={validationSchema}
            onSubmit={(values) => {
                // Convert blob URL to base64 before storing in navigation state
                if (logoImageUrl) {
                    // const img = new Image();
                    // img.onload = () => {
                    //     const canvas = document.createElement('canvas');
                    //     canvas.width = img.width;
                    //     canvas.height = img.height;
                    //     const ctx = canvas.getContext('2d');
                    //     ctx?.drawImage(img, 0, 0);

                    //     // Get base64 data URL
                    //     const base64Logo = canvas.toDataURL('image/png');

                    //     const cardDetails = {
                    //         firstName: values.firstName || "Jordan",
                    //         lastName: values.lastName || "Kwame Ellis",
                    //         jobTitle: values.jobTitle || "System Analyst Consultant",
                    //         colour: selectedColor,
                    //         logoImageUrl: base64Logo, // Store as base64 instead of blob URL
                    //         cardType: cardType || "11",
                    //         logoWidth: logoWidth,
                    //         logoHeight: logoHeight,
                    //         invertLogoColors: invertLogoColors,
                    //     };
                    //     updateCardDetails(cardDetails);
                    //     router.push(`/cart`);
                    // };
                    // img.src = logoImageUrl;

                    const cardDetails = {
                        firstName: values.firstName || "Jordan",
                        lastName: values.lastName || "Kwame Ellis",
                        jobTitle: values.jobTitle || "System Analyst Consultant",
                        colour: selectedColor,
                        logoImageUrl: logoImageUrl, // Store as blob URL
                        cardType: cardType || "11",
                        logoWidth: logoWidth,
                        logoHeight: logoHeight,
                        invertLogoColors: invertLogoColors,
                        imageFileName: imageFileName,
                        price: 10.00,
                        quantity: 1
                    };
                    updateCardDetails(cardDetails);
                    router.push(`/cart`);
                } else {
                    const cardDetails = {
                        firstName: values.firstName || "Jordan",
                        lastName: values.lastName || "Kwame Ellis",
                        jobTitle: values.jobTitle || "System Analyst Consultant",
                        colour: selectedColor,
                        logoImageUrl: "",
                        cardType: cardType || "11",
                        logoWidth: logoWidth,
                        logoHeight: logoHeight,
                        imageFileName: imageFileName,
                        price: 10.00,
                        quantity: 1
                    };
                    updateCardDetails(cardDetails);
                    router.push(`/cart`);
                }
            }}
        >
            {({ values }) => (
                <Form className="flex flex-col flex-1 mt-[1.19rem] px-[1rem] md:px-[2rem] xl:px-[4.38rem]">
                    <div>
                        <GoBackButton />
                    </div>

                    <div className="flex flex-1 gap-[1rem] lg:gap-[2rem] xl:gap-[7.69rem] lg:px-[4rem] xl:px-[7.68rem] mt-[0.69rem]">
                        {/* ---------------- left column (form) ---------------- */}
                        <div className="flex-col -lg:mx-auto flex-1 max-w-[27.75rem] relative overflow-y-auto hide-scrollbar">
                            <div className="absolute max-w-[27.75rem] pb-[6.63rem]">
                                <div className="text-[1.375rem] font-medium mb-[0.5rem]">Create your card</div>
                                <div className="w-full max-w-[27.75rem] text-subtext mb-[1.56rem]">
                                    Choose a template to showcase your personal brand, share your contact details,
                                    and present yourself professionally.
                                </div>

                                <div className="lg:hidden flex w-full mb-[1rem]">
                                    <div className="rounded-[0.625rem] w-full overflow-x-auto">
                                        <div className="flex mb-[0.5rem]">
                                            <div
                                                onClick={() => { }}
                                                className="flex justify-center bg-grey items-center p-[0.5rem] rounded-full"
                                            >
                                                <div className="text-headertext text-sm">GH₵10.00</div>
                                            </div>
                                        </div>
                                        <div className="">
                                            {cardType === "11" && (
                                                <BCard
                                                    firstName={values.firstName || "First name"}
                                                    lastName={values.lastName || "Last name(s)"}
                                                    bgColor={selectedColorObj?.hex || "#000000"}
                                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                                />
                                            )}
                                            {cardType === "12" && (
                                                <BCard
                                                    firstName={values.firstName || "First name"}
                                                    lastName={values.lastName || "Last name(s)"}
                                                    jobTitle={values.jobTitle || "System Analyst Consultant"}
                                                    bgColor={selectedColorObj?.hex || "#000000"}
                                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                                />
                                            )}
                                            {cardType === "13" && (
                                                <BCard
                                                    firstName={values.firstName || "First name"}
                                                    lastName={values.lastName || "Last name(s)"}
                                                    jobTitle={values.jobTitle || "System Analyst Consultant"}
                                                    logoText={!logoImageUrl ? "Your Logo" : undefined}
                                                    logoImageUrl={logoImageUrl}
                                                    logoHeight={logoHeight}
                                                    logoWidth={logoWidth}
                                                    bgColor={selectedColorObj?.hex || "#000000"}
                                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* colour picker */}
                                <div className="mb-[1.13rem]">
                                    <div className="text-[0.9375rem] text-headertext font-medium mb-[0.31rem]">
                                        Select card color
                                    </div>
                                    <div className="flex gap-[0.94rem]">
                                        {CARD_THEMES.map((c) => (
                                            <div
                                                key={c.id}
                                                onClick={() => setSelectedColor(c.id)}
                                                className={`flex border border-black items-center justify-center w-[3rem] h-[3rem] rounded-full ${c.color} cursor-pointer`}
                                            >
                                                {selectedColor === c.id && (
                                                    <img
                                                        src={c.blackCheck ? "/check-black.svg" : "/check-white.svg"}
                                                        className="w-[1rem] h-[1rem]"
                                                        alt="selected"
                                                    />
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* text fields */}
                                <div className="flex flex-col gap-[1.56rem]">
                                    <FormikAppTextInput
                                        name="firstName"
                                        label="Enter your first name"
                                        labelExtra="as you want it to appear on the card"
                                        placeholder="Cardholder first name"
                                    />
                                    <FormikAppTextInput
                                        name="lastName"
                                        label="Enter your last name"
                                        labelExtra="as you want it to appear on the card"
                                        placeholder="Cardholder last name"
                                    />
                                    {cardType !== "11" && (
                                        <FormikAppTextInput
                                            name="jobTitle"
                                            label="Enter your role"
                                            labelExtra="as you want it to appear on the card"
                                            placeholder="Cardholder role"
                                        />
                                    )}

                                    {/* logo uploader (only for template 2) */}
                                    {cardType === "13" && (
                                        <div className="flex flex-col gap-2">
                                            <label className="font-medium mb-[0.75rem] text-[0.9375rem] text-headertext">
                                                Upload your logo
                                            </label>
                                            <div
                                                className="border-dashed border-2 border-outline rounded-lg h-[8.44rem] pt-[1.44rem] pb-[1.33rem] flex items-center justify-center bg-inputbg cursor-pointer"
                                                onDrop={handleDrop}
                                                onDragOver={handleDragOver}
                                            >
                                                <input
                                                    id="fileInput"
                                                    type="file"
                                                    accept=".png, .svg"
                                                    onChange={handleImageUpload}
                                                    className="hidden"
                                                />
                                                <label htmlFor="fileInput" className="text-center text-gray-600 cursor-pointer">
                                                    {logoImageUrl ? (
                                                        <div className="flex flex-col items-center">
                                                            <img
                                                                src={logoImageUrl}
                                                                className="w-[2.38rem] h-[2.38rem] mb-[0.86rem] object-contain"
                                                                onLoad={handleImageLoad}
                                                                alt="Logo preview"
                                                            />
                                                            <div className="mb-[0.13rem] text-[0.9375rem] text-subheading">
                                                                Uploaded:&nbsp;
                                                                <span className="text-black underline">{imageFileName}</span>
                                                            </div>
                                                            <div
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    e.stopPropagation();
                                                                    setImageFile(null);
                                                                    setImageFileName(null);
                                                                    setLogoImageUrl(undefined)
                                                                }}
                                                                className="text-[0.8125rem] hover:text-rose-500 hover:font-semibold text-placeholder"
                                                            >
                                                                Click to remove
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div className="flex flex-col items-center">
                                                            <img src="/cloud-upload.svg" className="w-[2.38rem] h-[2.38rem] mb-[0.86rem]" />
                                                            <div className="mb-[0.13rem] text-[0.9375rem] text-subheading">
                                                                Drag and drop logo here or&nbsp;
                                                                <span className="text-black underline">Browse files</span>
                                                            </div>
                                                            <div className="text-[0.8125rem] text-placeholder">PNG or SVG files only</div>
                                                        </div>
                                                    )}
                                                </label>
                                            </div>

                                            {/* Logo size controls - only show when an image is uploaded */}
                                            {logoImageUrl && (
                                                <div className="mt-4 border border-outline rounded-lg p-4">
                                                    <div className="flex justify-between items-center mb-2">
                                                        <label className="text-[0.9375rem] text-headertext">Logo Size</label>
                                                        <div className="flex items-center">
                                                            <input
                                                                type="checkbox"
                                                                id="keepAspectRatio"
                                                                checked={keepAspectRatio}
                                                                onChange={(e) => setKeepAspectRatio(e.target.checked)}
                                                                className="mr-2 accent-black"
                                                            />
                                                            <label htmlFor="keepAspectRatio" className="text-[0.8125rem] text-subheading">
                                                                Keep aspect ratio
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div className="mb-3">
                                                        <div className="flex justify-between mb-1">
                                                            <label className="text-[0.8125rem] text-subheading">Width</label>
                                                            <span className="text-[0.8125rem] text-subheading">{logoWidth}%</span>
                                                        </div>
                                                        <input
                                                            type="range"
                                                            min="10"
                                                            max="200"
                                                            value={logoWidth}
                                                            onChange={handleWidthChange}
                                                            className="w-full accent-black"
                                                        />
                                                    </div>

                                                    <div>
                                                        <div className="flex justify-between mb-1">
                                                            <label className="text-[0.8125rem] text-subheading">Height</label>
                                                            <span className="text-[0.8125rem] text-subheading">{logoHeight}%</span>
                                                        </div>
                                                        <input
                                                            type="range"
                                                            min="10"
                                                            max="200"
                                                            value={logoHeight}
                                                            onChange={handleHeightChange}
                                                            className="w-full accent-black"
                                                        />
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>

                                {/* Add invert colors checkbox */}
                                {
                                    logoImageUrl &&
                                    <div className="flex items-center mt-2">
                                        <input
                                            type="checkbox"
                                            id="invertLogoColors"
                                            checked={invertLogoColors}
                                            onChange={(e) => handleInvertColors(e.target.checked)}
                                            className="mr-2 accent-black"
                                        />
                                        <label htmlFor="invertLogoColors" className="text-[0.8125rem] text-subheading">
                                            Invert logo colors
                                        </label>
                                    </div>
                                }

                                {/* submit */}
                                <button
                                    type="submit"
                                    className="flex mt-[1.5rem] items-center justify-center w-full max-w-[26.88rem] h-[3.13rem] rounded-[0.625rem] bg-black text-white font-medium"
                                >
                                    Add to cart
                                </button>
                            </div>
                        </div>

                        {/* ---------------- right column (live preview) ---------------- */}
                        <div className="-lg:hidden w-full max-w-[31.31rem] h-[27.63rem] rounded-[0.625rem] border border-outline pt-[1.88rem] pl-[1.88rem]">
                            <div className="flex mb-[2rem]">
                                <div className="flex justify-center bg-grey items-center px-[1.38rem] py-[1rem] rounded-full">
                                    <div className="text-headertext">GH₵10.00</div>
                                </div>
                            </div>

                            {cardType === "11" && (
                                <BCard
                                    scale={1.404}
                                    firstName={values.firstName || "First name"}
                                    lastName={values.lastName || "Last name(s)"}
                                    bgColor={selectedColorObj?.hex || "#000000"}
                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                />
                            )}
                            {cardType === "12" && (
                                <BCard
                                    scale={1.404}
                                    firstName={values.firstName || "First name"}
                                    lastName={values.lastName || "Last name(s)"}
                                    jobTitle={values.jobTitle || "Job title"}
                                    bgColor={selectedColorObj?.hex || "#000000"}
                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                />
                            )}
                            {cardType === "13" && (
                                <BCard
                                    scale={1.404}
                                    firstName={values.firstName || "First name"}
                                    lastName={values.lastName || "Last name(s)"}
                                    jobTitle={values.jobTitle || "Job title"}
                                    logoText={!logoImageUrl ? "Your Logo" : undefined}
                                    logoImageUrl={logoImageUrl}
                                    logoWidth={logoWidth}
                                    logoHeight={logoHeight}
                                    bgColor={selectedColorObj?.hex || "#000000"}
                                    textColor={selectedColorObj?.textColorHex || "#ffffff"}
                                />
                            )}
                        </div>
                    </div>
                </Form>
            )}
        </Formik>
    );
}
