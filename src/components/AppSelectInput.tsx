import React from "react";
import { BiChevronDown } from "react-icons/bi";

interface SelectInputProps {
    label?: string; // Label text
    labelExtra?: string; // Label extra text
    options: { value: string; label: string }[]; // Options for the select dropdown
    value?: string; // Selected value
    onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void; // Change handler
}

const AppSelectInput: React.FC<SelectInputProps> = ({
    label,
    labelExtra,
    options,
    value,
    onChange,
}) => {
    return (
        <div className="h-[4.69rem] flex flex-col justify-between relative">
            {
                label &&
                <label className="font-medium text-[0.9375rem] text-headertext">
                    {label}{" "}
                    <span className="text-placeholder">
                        {labelExtra && `(${labelExtra})`}
                    </span>
                </label>
            }
            <div className="relative">
                <select
                    value={value}
                    onChange={onChange}
                    className="outline-none bg-inputbg placeholder-placeholder border-outline border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] h-[3.13rem] w-full appearance-none"
                >
                    <option value="" disabled>
                        Select an option
                    </option>
                    {options.map((option, index) => (
                        <option key={index} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-placeholder">
                    <img src="/angle-small-down-gray.svg" className="w-[0.88rem] h-[0.88rem]" />
                </div>
            </div>
        </div>
    );
};

export default AppSelectInput;
