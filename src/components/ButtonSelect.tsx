import React from 'react';
import { BiCheckCircle } from 'react-icons/bi';

interface Option {
    label: string;
    value: string;
}

interface ButtonSelectProps {
    label?: string;
    options: Option[]; // List of options
    value: string; // Currently selected value
    onChange: (value: string) => void; // Callback when an option is selected
}

const ButtonSelect: React.FC<ButtonSelectProps> = ({ label, options, value, onChange }) => {
    return (
        <div>
            <div className="font-medium text-[0.9375rem] text-headertext">
                {label}
            </div>
            <div className="flex gap-4 mt-2">
                {options.map((option) => {
                    const isSelected = value === option.value;
                    return (
                        <div
                            key={option.value}
                            onClick={() => onChange(option.value)}
                            className={`flex items-center h-[2rem] px-4 font-medium rounded-full text-xs cursor-pointer ${isSelected
                                ? 'bg-black text-white gap-2'
                                : 'bg-grey text-headertext'
                                }`}
                        >
                            {option.label}
                            {isSelected && <BiCheckCircle className="text-white text-lg" />}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default ButtonSelect;
