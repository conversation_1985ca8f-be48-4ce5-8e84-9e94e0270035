import React from "react";

interface CheckItemProps {
  iconSrc: string; // Source URL for the icon
  title: string; // Title text
  description: string; // Description text
}

const CheckItem: React.FC<CheckItemProps> = ({ iconSrc, title, description }) => {
  return (
    <div className="flex items-start gap-[0.63rem]">
      <img src={iconSrc} className="h-[1rem] w-[1rem] mt-1" alt="Check icon" />
      <div className="flex flex-col gap-[0.06rem]">
        <div className="text-headertext text-[1.125rem] font-semibold">
          {title}
        </div>
        <div className="text-subtext w-[27.75rem]">{description}</div>
      </div>
    </div>
  );
};

export default CheckItem;
