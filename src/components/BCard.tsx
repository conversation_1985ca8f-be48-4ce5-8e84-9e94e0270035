import React from "react";

interface BCardProps {
  firstName: string; // First name of the person
  lastName: string; // Last name of the person
  jobTitle?: string; // Optional job title
  logoText?: string; // Optional logo (text)
  logoImageUrl?: string; // Optional logo (image URL)
  logoWidth?: number; // New prop for logo width
  logoHeight?: number; // New prop for logo height
  selected?: boolean;
  bgColor?: string; // Background color (default is black)
  textColor?: string; // Text color (default is white)
  scale?: number;
  transformOrigin?: string;
}

const BCard: React.FC<BCardProps> = ({
  firstName,
  lastName,
  jobTitle,
  logoText,
  logoImageUrl,
  logoWidth = 100, // Default to 100%
  logoHeight = 100, // Default to 100%
  selected = false,
  transformOrigin = "top left",
  bgColor = "black", // Default background color
  textColor = "white", // Default text color
  scale = 1,
}) => {
  return (
    <div
      className="flex flex-col w-[19.5rem] h-[10.51rem] shadow-lg rounded-[1rem]"
      style={{
        backgroundColor: bgColor,
        color: textColor,
        transformOrigin: transformOrigin,
        transform: `scale(${scale})`,
      }}
    >
      {/* First layout: First and Last Name */}
      {!jobTitle && !(logoText || logoImageUrl) && (
        <div className="flex items-center pt-4 flex-1 whitespace-nowrap pl-[1.1rem] w-[9.81rem] text-[1.25rem] font-medium">
          {firstName} <br /> {lastName}
        </div>
      )}

      {/* Second layout: First/Last Name with Job Title */}
      {jobTitle && !(logoText || logoImageUrl) && (
        <div className="flex flex-col justify-center flex-1 pt-5 ">
          <div className="whitespace-nowrap pl-[1.1rem] w-[9.81rem] text-[1.25rem] font-medium">
            {firstName} <br /> {lastName}
          </div>
          <div className="whitespace-nowrap text-[0.75rem] pl-[1.1rem] mt-[0.33rem]">
            {jobTitle}
          </div>
        </div>
      )}

      {/* Third layout: First/Last Name, Job Title, and logo */}
      {jobTitle && (logoText || logoImageUrl) && (
        <div className="flex flex-1 justify-between">
          <div className="flex flex-col justify-center flex-1 pt-5">
            <div className="whitespace-nowrap pl-[1.1rem] w-[9.81rem] text-[1.25rem] font-medium">
              {firstName} <br /> {lastName}
            </div>
            <div className="whitespace-nowrap w-[9.81rem] text-[0.75rem] pl-[1.1rem] mt-[0.33rem]">
              {jobTitle}
            </div>
          </div>
          <div className="pt-[1.33rem] pr-[1.06rem]">
            {logoImageUrl ? (
              <img
                src={logoImageUrl} // This will work with both blob URLs and base64
                alt="Logo"
                className="object-contain"
                style={{
                  height: `${logoHeight}%`,
                  width: `${logoWidth}%`,
                  maxHeight: '3rem',
                  transform: `scale(${logoWidth / 100}, ${logoHeight / 100})`,
                  transformOrigin: 'top right'
                }}
              />
            ) : (
            <div className="text-[1.47rem] whitespace-nowrap font-semibold">{logoText}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BCard;
