import React from "react";

interface ServiceListItemProps {
    onClick: () => void;
    imageSrc: string; // description URL of the service image
    altText: string; // Alt text for the image
    title: string; // Title of the service
    description: string; // description or website link text
}

const ServiceListItem: React.FC<ServiceListItemProps> = ({ imageSrc, altText, title, description, onClick }) => {
    return (
        <div onClick={onClick} className="flex flex-col cursor-pointer shadow bg-white border-[1px] border-outline rounded-[0.75rem] pb-[0.87rem]">
            <div className="h-[6.88rem] w-full">
                <img
                    src={imageSrc}
                    alt={altText}
                    className="h-full w-full object-cover rounded-t-[0.75rem]"
                />
            </div>

            <div className="bg-white pt-[0.57rem] px-[0.94rem] w-full">
                <div className="text-headertext font-medium">{title}</div>
                <div className="text-[0.8125rem] text-subtext whitespace-nowrap truncate">{description}</div>
            </div>
        </div>
    );
};

export default ServiceListItem;
