import React from 'react';

interface TextAreaFieldProps {
    label?: string; // Label text
    placeholder?: string; // Placeholder for the textarea
    value?: string; // Current value of the textarea
    onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void; // Change handler
    rows?: number; // Number of rows in the textarea, default is 4
}

const AppTextArea: React.FC<TextAreaFieldProps> = ({
    label,
    placeholder = '',
    value,
    onChange,
    rows = 4,
}) => {
    return (
        <div className="h-auto flex flex-col justify-between">
            <label className="font-medium text-[0.9375rem] text-headertext">{label}</label>
            <textarea
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                rows={rows}
                className="outline-none bg-inputbg placeholder-placeholder border-outline border-[0.0625rem] rounded-[0.625rem] text-[0.8125rem] px-[0.63rem] py-[0.5rem] resize-none"
            />
        </div>
    );
};

export default AppTextArea;
