import { BiX } from "react-icons/bi";
import { TAGS, TagValue } from "../../constants";

export default function TagComponent({ name, handleXClick, handleSelect }: { name: TagValue, handleXClick?: (value: TagValue) => void, handleSelect?: (value: TagValue) => void }) {
    const tag = TAGS.find((tag) => tag.text.toLowerCase() == name.toLowerCase())
    return (
        <div onClick={()=>handleSelect?.(name)}
            className={`flex items-center gap-1 px-2 py-1 rounded-full ${tag?.wrapperClass}`}
        >
            <span className={`${tag?.textClass}`}>{tag?.text}</span>
            {
                handleXClick &&
                <BiX
                    className="cursor-pointer"
                    onClick={(e) => {
                        e.stopPropagation();
                        handleXClick(name);
                    }}
                />
            }
        </div>
    )
}