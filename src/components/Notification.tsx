import React, { <PERSON>actN<PERSON>, MouseEvent, useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { BiCheckDouble, BiCross, BiInfoSquare, BiX, BiXCircle } from 'react-icons/bi';
import { ImCheckboxChecked, Im<PERSON>ross, ImInfo, ImWarning, ImXing } from 'react-icons/im';

type notifType = "success" | "failure" | "info" | "warn"

interface NotificationToastProps {
    isOpen: boolean;
    renderObj: {
        leftSection?: ReactNode;
        midSection: ReactNode;
        rightSection?: ReactNode;
    }
    type: notifType
}

const classMap: Record<notifType, string> = {
    "success": "text-teal-800 shadow-teal-600",
    "failure": "text-red-800 shadow-red-600",
    "info": "text-sky-800 shadow-sky-600",
    "warn": "text-amber-800 shadow-amber-600",
}

const iconMap: Record<notifType, ReactNode> = {
    "success": <ImCheckboxChecked className="text-teal-500" size={"1.5rem"} />,
    "failure": <ImCross className="text-red-600" size={"1.5rem"} />,
    "info": <ImInfo className="text-sky-600" size={"1.5rem"} />,
    "warn": <ImWarning className="text-amber-600" size={"1.5rem"} />,
}

const NotificationToast: React.FC<NotificationToastProps> = ({ isOpen, renderObj, type }) => {
    const [isToastOpen, setIsToastOpen] = useState(isOpen)

    useEffect(() => {
        setIsToastOpen(isOpen)
    }, [isOpen])

    const notifRoot = typeof document !== 'undefined' ? document.getElementById('notification-root') : null;
    if (!notifRoot) return null;

    return ReactDOM.createPortal(
        <div className={`fixed z-[15] left-[50%] -translate-x-[50%] transition-transform transform duration-300 ${isToastOpen ? 'translate-y-0' : '-translate-y-full'} flex justify-center`}>
            <div className={`${classMap[type]} min-w-[15rem] flex mt-5 text-[1rem] font-medium items-center gap-[0.6rem] bg-white rounded shadow-sm pl-[0.3rem] pr-[0.25rem] py-[0.5rem]`}>
                {renderObj?.leftSection || iconMap[type]}
                {renderObj?.midSection}
                <BiX onClick={() => { setIsToastOpen(false) }} className='text-subtext cursor-pointer ml-auto' size={"1rem"} />
            </div>
        </div>,
        notifRoot
    );
};

export default NotificationToast;
