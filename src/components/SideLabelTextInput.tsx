import React, { useState } from 'react';
import { AiOutlineEye } from 'react-icons/ai'; // Import React Icons

interface InputFieldProps {
    label?: string;
    placeholder?: string; // Placeholder for the input field
    value?: string; // Current value of the input field
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void; // Change handler
    type?: string; // Input type, default is "text"
}

const SideLabelTextInput: React.FC<InputFieldProps> = ({
    label,
    placeholder = '',
    value,
    onChange,
    type = 'text',
}) => {
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const isPasswordType = type === 'password';

    return (
        <div className="relative">
            <div className='flex'>
                <div className='border-outline border-[0.0625rem] text-placeholder rounded-l-[0.4375rem] pl-[1.13rem] pr-[2.44rem] pt-[1rem] pb-[1.13rem] h-[3.44rem] pr-[2.5rem]'>
                    {label}
                </div>
                <div className='flex-1'>
                    <input
                        type={isPasswordType && showPassword ? 'text' : type}
                        placeholder={placeholder}
                        value={value}
                        onChange={onChange}
                        className="outline-none w-full bg-transparent placeholder-placeholder border-outline border-[0.0625rem] rounded-r-[0.4375rem] px-[1.13rem] pt-[1rem] pb-[1.13rem] h-[3.44rem] pr-[2.5rem]"
                    />
                </div>
            </div>
            {isPasswordType && (
                <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-placeholder focus:outline-none"
                >
                    {showPassword ? (
                        <AiOutlineEye className="w-5 h-5" />
                    ) : (
                        <img src="/eye-crossed.svg" alt="Hide password" className="w-4 h-4" />
                    )}
                </button>
            )}
        </div>
    );
};

export default SideLabelTextInput;
