import React, { ReactN<PERSON>, MouseEvent, useEffect } from 'react';
import ReactDOM from 'react-dom';

interface ModalProps {
    isOpen: boolean;
    children: ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, children }) => {
    if (!isOpen) return null;

    const modalRoot = typeof document !== 'undefined' ? document.getElementById('modal-root') : null;
    if (!modalRoot) return null;

    return ReactDOM.createPortal(
        <div>
            {children}
        </div>,
        modalRoot
    );
};

export default Modal;
