import { useEffect, useState } from "react";

const useGeolocation = () => {
  const [location, setLocation] = useState<{ city?: string; country?: string; continent?: string } | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser.");
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;

        try {
          const res = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`);
          const data = await res.json();

          setLocation({
            city: data.address.city || data.address.town || data.address.village,
            country: data.address.country,
            continent: data.address.continent, // May not always be available
          });
        } catch (err) {
          setError("Failed to fetch location details.");
        }
      },
      (err) => {
        setError(err.message);
      }
    );
  }, []);

  return { location, error };
};

export default useGeolocation