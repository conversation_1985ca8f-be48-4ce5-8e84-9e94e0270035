{"name": "beyond", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.66.11", "@tanstack/react-query-devtools": "^5.66.11", "axios": "^1.7.9", "chart.js": "^4.4.7", "cheerio": "^1.0.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "next": "15.1.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-tooltip": "^5.28.0", "usehooks-ts": "^3.1.1", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}