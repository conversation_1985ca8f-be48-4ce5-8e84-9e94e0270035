import { EmailVisibility } from "./constants";

export interface UserMetrics {
    usersId: number;
    cardTaps: number;
    contactExchange: number;
    locationInsights: string; // Consider using [number, number] if always lat/lng
    cardsId: string;
}

export interface Testimonial {
    id?: number;
    userId?: number;
    testimonial?: string;
    writerName?: string;
    writerRole?: string;
    writerCompany?: string;
    emailAddress?: string;
    optionalNote?: string;
    status?: boolean;
    archived?: boolean;
    emailVisibility?: EmailVisibility;
    approvalTime?: string;
}

export interface SocialProof {
    title?: string;
    links: string;
}
